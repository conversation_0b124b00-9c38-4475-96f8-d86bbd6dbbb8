const { z } = require('zod');
const { sort, filter } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');
const { createIdParamsSchema } = require('../../app/utils');

const getSteelbarsSchema = z.object({
  search: z.string().optional(),
  sort: z.enum(['asc', 'desc']).optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  filterBy: z
    .string()
    .transform((str) => {
      try {
        return JSON.parse(str);
      } catch {
        return {};
      }
    })
    .optional(),
});

const updateOfmAcctCdSchema = z
  .object({
    steelbarsId: z.number(),
    ofmItemAcctCd: z.string(),
  })
  .strict();

const steelbarsSortSchema = sortSchema(sort.STEELBARS_SORT_COLUMNS);
const steelbarsFilterSchema = filterSchema(filter.STEELBARS_FILTER_COLUMNS);

const getSteelbarsParams = z
  .object({
    id: createIdParamsSchema('Steelbar Id'),
  })
  .strict();

const createSteelbarSchema = z
  .object({
    grade: z
      .string()
      .min(1, {
        message: 'Please complete all required fields before proceeding',
      })
      .max(20)
      .or(z.literal(null).transform(() => '')),
    diameter: z
      .number()
      .positive('Please complete all required fields before proceeding'),
    length: z
      .number()
      .positive('Please complete all required fields before proceeding'),
    weight: z
      .number()
      .positive('Please complete all required fields before proceeding'),
    ofm_acctcd: z.string().max(50).optional(),
    created_at: z.date().optional(),
    updated_at: z.date().optional(),
    unit: z.string().optional(),
  })
  .strict();

module.exports = {
  getSteelbarsSchema,
  steelbarsSortSchema,
  steelbarsFilterSchema,
  getSteelbarsParams,
  updateOfmAcctCdSchema,
  createSteelbarSchema,
};
