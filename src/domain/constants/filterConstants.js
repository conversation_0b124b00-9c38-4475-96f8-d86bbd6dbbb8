const FILTER_TYPES = {
  STRING: 'string',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  NUMBER_ARRAY: 'numberArray',
  STRING_ARRAY: 'stringArray',
};

// TODO: Add more filter columns as needed
const USER_FILTER_COLUMNS = {
  roleId: FILTER_TYPES.NUMBER_ARRAY,
  username: FILTER_TYPES.STRING,
};

const REQUISITION_FILTER_COLUMNS = {
  ref_number: FILTER_TYPES.STRING,
  type: FILTER_TYPES.STRING,
  requestor: FILTER_TYPES.STRING,
  company: FILTER_TYPES.STRING,
  project_department: FILTER_TYPES.STRING,
  status: FILTER_TYPES.STRING,
  updated_at: FILTER_TYPES.STRING,
};

const NON_OFM_ITEM_FILTER_COLUMNS = {
  itemName: FILTER_TYPES.STRING,
  unit: FILTER_TYPES.STRING,
  trade: FILTER_TYPES.STRING,
  itemType: FILTER_TYPES.STRING,
};

const ITEM_FILTER_COLUMNS = {
  itmDes: FILTER_TYPES.STRING,
  unit: FILTER_TYPES.STRING,
  trade: FILTER_TYPES.STRING,
  acctCd: FILTER_TYPES.STRING,
  itemCd: FILTER_TYPES.STRING,
};

const DEPARTMENT_FILTER_COLUMNS = {
  name: FILTER_TYPES.STRING,
  code: FILTER_TYPES.STRING,
};

const ITEM_LIST_FILTER_COLUMNS = {
  listName: FILTER_TYPES.STRING,
  company: FILTER_TYPES.STRING,
  project: FILTER_TYPES.STRING,
  trade: FILTER_TYPES.STRING,
};

const COMPANY_FILTER_COLUMNS = {
  name: FILTER_TYPES.STRING,
};

const PROJECT_FILTER_COLUMNS = {
  name: FILTER_TYPES.STRING,
};

const SUPPLIER_FILTER_COLUMNS = {
  name: FILTER_TYPES.STRING,
  status: FILTER_TYPES.STRING,
};

const HISTORY_FILTER_COLUMNS = {
  rsNumber: FILTER_TYPES.STRING,
};

const STEELBARS_FILTER_COLUMNS = ['grade'];

const NON_RS_FILTER_COLUMNS = {
  nonRsNumber: FILTER_TYPES.STRING,
};

const NON_RS_HISTORY_FILTER_COLUMNS = {
  approver: FILTER_TYPES.STRING,
};

const CANVASS_FILTER_COLUMNS = {
  requisitionId: FILTER_TYPES.NUMBER,
  canvassNumber: FILTER_TYPES.STRING,
};

module.exports = {
  USER_FILTER_COLUMNS,
  NON_OFM_ITEM_FILTER_COLUMNS,
  DEPARTMENT_FILTER_COLUMNS,
  ITEM_FILTER_COLUMNS,
  REQUISITION_FILTER_COLUMNS,
  ITEM_LIST_FILTER_COLUMNS,
  COMPANY_FILTER_COLUMNS,
  PROJECT_FILTER_COLUMNS,
  SUPPLIER_FILTER_COLUMNS,
  HISTORY_FILTER_COLUMNS,
  STEELBARS_FILTER_COLUMNS,
  NON_RS_FILTER_COLUMNS,
  NON_RS_HISTORY_FILTER_COLUMNS,
  CANVASS_FILTER_COLUMNS,
};
