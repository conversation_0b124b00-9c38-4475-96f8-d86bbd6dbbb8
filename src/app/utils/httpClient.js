const clientError = require('../errors/clientErrors');

class HTTPClient {
  constructor ({ endpoint, logger = null }) {
    this.endpoint = endpoint;
    this.logger = logger;
    this.HTTP_METHODS = {
      GET: 'GET',
      POST: 'POST',
      PUT: 'PUT',
      DELETE: 'DELETE',
    };
  }

  async request({ method, path = null, body = null, headers = null }) {
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      Accept: 'application/json',
    };

    const options = {
      method,
      headers: { ...defaultHeaders, ...headers },
      body: body ? JSON.stringify(body) : null,
    };

    this.logger.info('=== REQUEST OPTIONS ===');
    this.logger.info(`Endpoint: ${this.endpoint}${path}`);
    this.logger.info({ options });

    // Create an AbortController for timeout handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

    try {
      const response = await fetch(`${this.endpoint}${path}`, {
        ...options,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return await this.processResponse(response);
    } catch (error) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw clientError.BAD_REQUEST({
          message: 'Request timeout',
          description: 'The external API request timed out after 60 seconds',
          errorCode: 'REQUEST_TIMEOUT',
        });
      }
      throw error;
    }
  }

  async processResponse(response) {
    // Read the response body once
    const responseText = await response.text();
  
    if (!response.ok) {
      this.logger.info({
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers),
        body: responseText,
      });

      throw clientError.BAD_REQUEST({
        message: 'External API request failed',
        description:
          responseText || 'Failed to communicate with external service',
        errorCode: 'EXTERNAL_API_ERROR',
      });
    }

    // Parse the text as JSON
    return JSON.parse(responseText);
  }

  async get({ path, headers = null }) {
    this.logger.info('=== HTTPClient GET DEBUG ===');
    this.logger.info('Path:', path);
    this.logger.info('Endpoint:', this.endpoint);
    this.logger.info('Is Mock:', this.isMockEnvironment());

    this.logger.info('=== MAKING REAL GET REQUEST TO MOCKTAIL ===');
    const response = await this.request({
      method: this.HTTP_METHODS.GET,
      path,
      headers,
    });

    // Handle mock API response format - extract data array if it exists
    if (
      this.isMockEnvironment() &&
      response.data &&
      Array.isArray(response.data)
    ) {
      return response.data;
    }

    return response;
  }

  async post({ path, body = null, headers = null }) {
    // Handle mock authentication for development/testing
    this.logger.info('=== HTTPClient POST DEBUG ===');
    this.logger.info('Path:', path);
    this.logger.info('Endpoint:', this.endpoint);
    this.logger.info('Is Mock:', this.isMockEnvironment());

    if (path === '/token/' && this.isMockEnvironment()) {
      this.logger.info('=== RETURNING MOCK TOKEN ===');
      return {
        access: 'mock-token-12345',
        refresh: 'mock-refresh-token-12345',
        expires_in: 3600,
      };
    }

    this.logger.info('=== MAKING REAL REQUEST ===');
    return await this.request({
      method: this.HTTP_METHODS.POST,
      path,
      body,
      headers,
    });
  }

  isMockEnvironment() {
    const isMock =
      this.endpoint?.includes('mocktail') ||
      this.endpoint?.includes('beeceptor') ||
      process.env.NODE_ENV === 'local';
    this.logger.info('=== MOCK ENV CHECK ===');
    this.logger.info('Endpoint:', this.endpoint);
    this.logger.info('NODE_ENV:', process.env.NODE_ENV);
    this.logger.info('Is Mock:', isMock);
    return isMock;
  }

  async put({ path, body = null, headers = null }) {
    return await this.request({
      method: this.HTTP_METHODS.PUT,
      path,
      body,
      headers,
    });
  }

  async delete({ path, headers = null }) {
    return await this.request({
      method: this.HTTP_METHODS.DELETE,
      path,
      headers,
    });
  }
}

module.exports = HTTPClient;
