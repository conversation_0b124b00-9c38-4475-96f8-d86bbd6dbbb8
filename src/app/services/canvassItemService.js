class CanvassItemService {
  constructor({ clientErrors, canvassItemRepository }) {
    this.clientErrors = clientErrors;
    this.canvassItemRepository = canvassItemRepository;
  }

  async getAllCanvassItems(payload = {}, options = {}) {
    const { canvassId, query = {}, order = [] } = payload;
    const { sortBy, filterBy, ...queries } = query;
    const canvassItemList = await this.canvassItemRepository.getAllCanvassItems(
      {
        ...queries,
        whereClause: {
          canvassRequisitionId: canvassId,
        },
        order,
      },
      options,
    );

    return canvassItemList;
  }

  async getCanvassItemById(canvassItemId) {
    const canvassItem =
      await this.canvassItemRepository.getCanvassItemById(canvassItemId);

    if (!canvassItem) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Canvass item not found',
      });
    }

    return canvassItem;
  }
}

module.exports = CanvassItemService;
