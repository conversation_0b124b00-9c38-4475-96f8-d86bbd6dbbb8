class CitylandApiService {
  constructor(container) {
    const { utils, fastify } = container;

    this.utils = utils;
    this.fastify = fastify;
    this.httpClient = new utils.HTTPClient({
      endpoint: `${process.env.CITYLAND_API_URL}`,
      logger: fastify.log,
    });
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  async getAccessToken() {
    try {
      // Check if we already have a valid token
      if (
        this.accessToken &&
        this.tokenExpiry &&
        new Date() < this.tokenExpiry
      ) {
        this.fastify.log.info('Using cached Cityland API access token');
        return this.accessToken;
      }

      this.fastify.log.info('Requesting new Cityland API access token');
      const authResult = await this.httpClient.post({
        path: '/token/',
        body: {
          username: process.env.CITYLAND_API_USERNAME,
          password: process.env.CITYLAND_API_PASSWORD,
        },
      });

      if (!authResult.access) {
        throw new Error('Failed to get access token from Cityland API');
      }

      this.accessToken = authResult.access;

      // Set token expiry to 30 minutes
      this.tokenExpiry = new Date();
      this.tokenExpiry.setMinutes(this.tokenExpiry.getMinutes() + 30);

      return this.accessToken;
    } catch (error) {
      this.fastify.log.error(
        `Error getting Cityland API access token: ${error.message}`,
      );
      throw error;
    }
  }

  async request(method, path, body = null) {
    try {
      const token = await this.getAccessToken();

      const options = {
        path,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      if (body) {
        options.body = body;
      }

      return await this.httpClient[method.toLowerCase()](options);
    } catch (error) {
      this.fastify.log.error(
        `Error making request to Cityland API: ${error.message}`,
      );
      throw error;
    }
  }

  async get(path) {
    return this.request('get', path);
  }

  async post(path, body) {
    return this.request('post', path, body);
  }

  async put(path, body) {
    return this.request('put', path, body);
  }

  async delete(path) {
    return this.request('delete', path);
  }
}

module.exports = CitylandApiService;
