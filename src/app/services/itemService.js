const fs = require('fs');
const path = require('path');

class ItemService {
  constructor ({
    itemRepository,
    syncRepository,
    ofmItemListRepository,
    nonOfmItemRepository,
    clientErrors,
    fastify,
    utils,
    entities,
    historyRepository,
    requisitionItemListRepository,
    notificationService,
    userService,
    constants,
    db,
    requisitionBadgeRepository,
    roleService,
    projectTradeService,
    syncService
  }) {
    this.httpClient = new utils.HTTPClient({
      endpoint: `${process.env.CITYLAND_API_URL}`,
      logger: fastify.log,
    });
    this.userService = userService;
    this.itemRepository = itemRepository;
    this.syncRepository = syncRepository;
    this.ofmItemListRepository = ofmItemListRepository;
    this.nonOfmItemRepository = nonOfmItemRepository;
    this.fastify = fastify;
    this.clientErrors = clientErrors;
    this.utils = utils;
    this.entities = entities;
    this.db = db;
    this.historyRepository = historyRepository;
    this.requisitionItemListRepository = requisitionItemListRepository;
    this.notificationService = notificationService;
    this.constants = constants;
    this.requisitionBadgeRepository = requisitionBadgeRepository;
    this.roleService = roleService;
    this.projectTradeService = projectTradeService;
    this.syncService = syncService;
  }

  extractCodesFromAcctCd(acctCd) {
    if (!acctCd || acctCd.length < 11) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Invalid acctCd length: ${acctCd}`,
      });
    }

    return {
      projectCode: acctCd.substring(4, 7),
      tradeCode: parseInt(acctCd.substring(9, 11), 10),
    };
  }

  transformItemData(rawItem) {
    return {
      itemCd: rawItem.ACCOUNT_CODE.trim(),
      itmDes: rawItem.ITEM_DESCRIPTION.trim(),
      unit: '',
      acctCd: rawItem.ACCOUNT_CODE.trim(),
      gfq: rawItem.GFQ || 1000, // Mock gfq for now since cityland api does not have gfq
      remainingGfq: rawItem.GFQ || 1000, // Mock remaining gfq for now since cityland api does not have remaining gfq
      tradeCode: parseInt(rawItem.ACCOUNT_CODE.substring(9, 11), 10),
    };
  }

  createOfmListData(projectData, tradeData, codes) {
    if (!projectData?.name || !tradeData?.tradeName) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Invalid acctCd length: ${(projectData, tradeData, codes)}`,
      });
    }

    const listName = `${projectData.name}-${tradeData.tradeName}`;
    const data = {
      listName,
      projectCode: codes.projectCode,
      tradeCode: codes.tradeCode,
      companyCode: projectData.company?.code,
    };

    return data;
  }

  async syncItemsAtBackground(userFromToken) {
    return this.syncItems(userFromToken);
  }

  async syncItems(userFromToken) {
    this.fastify.log.info('Syncing items...');
    const authResult = await this.httpClient.post({
      path: '/token/',
      body: {
        username: process.env.CITYLAND_API_USERNAME,
        password: process.env.CITYLAND_API_PASSWORD,
      },
    });
    const result = await this.httpClient.get({
      path: '/items',
      headers: {
        Authorization: `Bearer ${authResult.access}`,
      },
    });

    this.logToFile({
      message: 'START_PROCESS',
      rows: result.length,
      data: result,
    });

    const { CANVASS_STATUS } = this.constants.canvass;
    const { PO_STATUS } = this.constants.purchaseOrder;
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;
    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;

    const batchSize = 100;
    const createdItemsArray = [];

    for (let i = 0; i < result.length; i += batchSize) {
      this.logToFile({ message: `BATCH_${i / batchSize + 1}: started` });

      const batch = result.slice(i, i + batchSize);
      const {
        createdItems,
        newCreatedItems,
      } = await this.processItemSync({
        result: batch,
        userFromToken,
        CANVASS_STATUS,
        PO_STATUS,
        RS_PAYMENT_REQUEST_STATUS,
        NOTIFICATION_TYPES,
        NOTIFICATION_DETAILS,
      });


      createdItemsArray.push(...createdItems, ...newCreatedItems);
      this.logToFile({ message: `BATCH_${i / batchSize + 1}: completed` });
      await this.randomDelay();
    }

    const lastSynced = await this.syncRepository.updateLastSynced('item');
    const rolesToNotif = await this.roleService.getSyncRecipientsRoleId();

    await Promise.all(rolesToNotif.map(async (roleId) => {
      await this.notificationService.sendNotification({
        title: NOTIFICATION_DETAILS.ITEM_SYNC_DONE.title,
        message: NOTIFICATION_DETAILS.ITEM_SYNC_DONE.message,
        type: NOTIFICATION_TYPES.ITEM_SYNC,
        recipientRoleId: roleId,
        senderId: userFromToken.id,
      });
    }));

    try {
      if (createdItemsArray.length > 0) {
        await this.syncService.queueSync({
          userFromToken,
          createdItems: createdItemsArray,
          type: 'item_modal',
          withTimestamp: false,
        });
      }
    } catch (error) {
      this.logToFile({ message: 'queue sync', error });

      throw error;
    }

    return { lastSynced };
  }

  async getItemById(id) {
    try {
      const item = await this.itemRepository.getItemDetails(id);

      if (!item) {
        throw this.clientErrors.NOT_FOUND({
          message: 'Item not found',
        });
      }

      const codes = this.extractCodesFromAcctCd(item.acctCd);

      let projectDetails = null;
      if (codes) {
        projectDetails =
          await this.ofmItemListRepository.findProjectWithCompany(
            codes.projectCode,
          );
      }

      return {
        ...item,
        trade: item.trade?.code ? item.trade : null,
        projectName: projectDetails ? projectDetails.name : null,
        companyName: projectDetails ? projectDetails.company?.name : null,
      };
    } catch (error) {
      throw error;
    }
  }

  async createNonOfmItem(data) {
    try {
      return await this.nonOfmItemRepository.create(data);
    } catch (error) {
      console.log(error, 'THIS IS THE ERROR');
      if (error.name === 'SequelizeUniqueConstraintError') {
        throw this.clientErrors.BAD_REQUEST({
          message: 'NonOfmItem with this name already exists',
        });
      }
      throw error;
    }
  }

  async getAllNonOfmItems(params) {
    const tradeCondition = {};
    const { sortBy, filterBy, ...queries } = params;
    const { nonOfmItemSortSchema, nonOfmItemFilterSchema } =
      this.entities.nonOfmItem;

    const parsedSortBy = nonOfmItemSortSchema.parse(sortBy);
    const parsedFilterBy = nonOfmItemFilterSchema.parse(filterBy);
    const { trade, ...restFilter } = parsedFilterBy || {};
    const filterWhereClause = this.utils.buildFilterWhereClause(restFilter);

    if (trade) {
      tradeCondition['$trade.trade_name$'] = {
        [this.db.Sequelize.Op.iLike]: `%${trade}%`,
      };
    }

    return await this.nonOfmItemRepository.getAllNonOfmItems({
      ...queries,
      order: parsedSortBy,
      filterBy: filterWhereClause,
      whereClause: {
        ...tradeCondition,
      },
    });
  }

  async getNonOfmItemById(id) {
    return await this.nonOfmItemRepository.getNonOfmItemById(id);
  }

  async updateNonOfmItem(id, data) {
    try {
      return await this.nonOfmItemRepository.updateById(id, data);
    } catch (error) {
      if (error.name === 'SequelizeUniqueConstraintError') {
        throw this.clientErrors.BAD_REQUEST({
          message: 'NonOfmItem with this name already exists',
        });
      }
      throw error;
    }
  }

  async getAllOfmItemLists(params) {
    const { data: projectTrades } =
      await this.projectTradeService.getAllEngineerProjectTrades(params.userId);

    // If user is an engineer not assigned to any trade, return empty results
    if (
      params.role === this.constants.user.USER_TYPES.ENGINEERS &&
      (!projectTrades || projectTrades.length === 0)
    ) {
      return {
        data: [],
        total: 0,
      };
    }

    // Extract all tradeIds and projectIds from user's assignments
    const tradeIds = projectTrades.map((pt) => pt.tradeId).filter(Boolean);
    const projectIds = projectTrades.map((pt) => pt.projectId).filter(Boolean);

    if (
      params.role === this.constants.user.USER_TYPES.ENGINEERS &&
      tradeIds.length === 0
    ) {
      return {
        data: [],
        total: 0,
      };
    }

    // Filter OFM items by user's assigned trades and projects
    const ofmItemListResults =
      await this.ofmItemListRepository.getAllOfmItemLists({
        ...params,
        tradeIds,
        projectIds,
      });

    return ofmItemListResults;
  }

  async getAllOfmListItems(params) {
    return await this.ofmItemListRepository.getAllOfmListItems(params);
  }

  async getOfmItemListById(id) {
    try {
      const ofmItemList =
        await this.ofmItemListRepository.getOfmItemListDetails(id);

      if (!ofmItemList) {
        throw this.clientErrors.NOT_FOUND({
          message: 'OFM Item List not found',
        });
      }

      return ofmItemList;
    } catch (error) {
      throw error;
    }
  }

  async getOfmItemListById(id) {
    try {
      const ofmItemList =
        await this.ofmItemListRepository.getOfmItemListDetails(id);

      if (!ofmItemList) {
        throw this.clientErrors.NOT_FOUND({
          message: 'OFM Item List not found',
        });
      }

      return ofmItemList;
    } catch (error) {
      throw error;
    }
  }

  // async getOfmItemsByListId(id, params) {
  //   try {
  //     const items = await this.ofmItemListRepository.getOfmItemsByListIdDetails(
  //       id,
  //       params,
  //     );

  //     if (!items.data || items.data.length === 0) {
  //       throw this.clientErrors.NOT_FOUND({
  //         message: 'No items found for this OFM list',
  //       });
  //     }

  //     return items;
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  async getHistory(request) {
    this.fastify.log.info(`Getting Item History`);
    const result = await this.itemRepository.getAllHistory(request);
    this.fastify.log.info(`Successfully Retrieved Item History`);
    return result;
  }

  async getItemHistory(request) {
    return await this.historyRepository.getAllItemsHistory(request);
  }

  async updateOfmItem(id, payload) {
    const item = await this.itemRepository.getById(id);
    if (!item) {
      throw this.clientErrors.NOT_FOUND({ message: 'Item not found' });
    }

    return await this.itemRepository.update({ id }, payload);
  }

  async bulkUpdateOfmItems(itemsArray) {
    if (!Array.isArray(itemsArray) || itemsArray.length === 0) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Request body must be a non-empty array of items',
      });
    }

    for (const item of itemsArray) {
      if (!item.id) {
        throw this.clientErrors.BAD_REQUEST({
          message: 'Each item must have an id field',
        });
      }
    }

    const itemIds = itemsArray.map((item) => item.id);
    const existingItems = await this.itemRepository.findAll({
      where: { id: itemIds },
      paginate: false,
    });

    if (existingItems.data.length !== itemIds.length) {
      const existingIds = new Set(existingItems.data.map((item) => item.id));
      const missingIds = itemIds.filter((id) => !existingIds.has(id));
      throw this.clientErrors.NOT_FOUND({
        message: `The following items were not found: ${missingIds.join(', ')}`,
      });
    }

    return await this.itemRepository.bulkUpdateOfmItems(itemsArray);
  }

  async getItemsUniqueUnits({ type = 'OFM' }) {
    const { REQUEST_TYPES } = this.constants.item;
    const { NON_OFM } = REQUEST_TYPES;

    if (type === NON_OFM) {
      return await this.nonOfmItemRepository.getUniqueUnits();
    }

    return await this.itemRepository.getUniqueUnits();
  }

  async getItemPurchaseHistory(params) {
    return await this.itemRepository.getItemPurchaseHistory(params);
  }

  async logToFile({ message, data = '' }) {
    const logFile = path.join(
      __dirname,
      `../../infra/logs/${path.basename(__filename)}.log`,
    );
    if (!fs.existsSync(logFile)) {
      fs.writeFileSync(logFile, '');
    }

    let logMessage = `${new Date().toISOString()}: ${message}`;

    if (data) {
      logMessage += ` ${JSON.stringify(data, null, 2)}`;
    }

    fs.appendFileSync(logFile, `${logMessage}\n`);
  }

  async processItemSync({
    result,
    userFromToken,
    CANVASS_STATUS,
    PO_STATUS,
    RS_PAYMENT_REQUEST_STATUS,
    NOTIFICATION_TYPES,
    NOTIFICATION_DETAILS,
  }) {
    try {
      let newItems = [];

      //adjust the necesary properties
      result.forEach((item) => {
        newItems.push(this.transformItemData(item));
      });

      let items = newItems;
      this.logToFile({ message: `ITEMS: ${JSON.stringify(items)}` });

      const uniqueItemCDs = new Set();
      const processedCombinations = new Set();
      const newCreatedItems = [];
      const mappedItems = [];
      const ofmItemLists = [];
      const { id: purchasingHeadId } =
        (await this.roleService.getPurchasingHeadUserId()) || {};
      let pickItems = [];
      let toUpdate;
      this.logToFile({ message: `PROCESS_ITEM_SYNC: started` });
      this.logToFile({
        message: `PROCESS_ITEM_SYNC: Checking Database for Items...`,
      });

      const { data: oldItems } = await this.itemRepository.findAll({
        paginate: false,
      });

      this.logToFile({ message: `OLD_ITEM_COUNT: ${oldItems.length}` });

      const pick = (obj, arr) =>
        arr.reduce(
          (acc, record) => (record in obj && (acc[record] = obj[record]), acc),
          {},
        );

      if (oldItems.length !== 0) {
        this.logToFile({
          message: `PROCESS_ITEM_SYNC: Items Found in Database`,
        });

        const keys = Object.keys(newItems[0]);

        //pick the correct necessary details regarding each item
        oldItems.forEach((item) => pickItems.push(pick(item, keys)));

        // to filter what changed old vs new
        toUpdate = newItems.filter(
          (item) =>
            !pickItems.some(
              (old) => JSON.stringify(item) === JSON.stringify(old),
            ),
        );

        // update/create the new Item Values
        this.logToFile({
          message: `PROCESS_ITEM_SYNC: Found New Items/Values to Sync`,
        });
        const upsert = await Promise.all(
          toUpdate.map(
            async (item) => await this.itemRepository.upsertItem(item), // optimize this
          ),
        );

        upsert.forEach((item) => {
          if (item.created) {
            newCreatedItems.push(item.created);
          }
        });

        this.logToFile({ message: `ITEM_UPSERT: ${JSON.stringify(upsert)}` });

        items = upsert;

        //get all Requisition with updated Item
        const requisitions = await Promise.all(
          upsert.map(
            async (item) =>
              this.requisitionItemListRepository.getAllItemsByReqId(item.id), // optimize this. instead of loop. get all requisitions at once using id
          ),
        );

        this.logToFile({
          message: `PROCESS_ITEM_SYNC: Notifying users and Create RS Badge`,
        });

        //notifying all upserted items
        //iterate each group of requisitions respect with their item
        for (const groups of requisitions) {
          if (groups.length === 0) {
            this.logToFile({
              message: `PROCESS_ITEM_SYNC: No users to notify`,
            });
          }
          //iterate updated items that will notify their respective requisition
          for (const item of groups) {
            //item has no assigned requisition
            //for all draft and for approval(submitted) notify users

            //Scenario 2 RS status draft or submitted
            if (item.requisition.status === 'rs_draft' || 'for_rs_approval') {
              this.logToFile({
                message: `PROCESS_ITEM_SYNC: Scenario 2: Notifying Users and Approvers`,
              });
              const approvers = [];
              if (item.requisition.requisitionApprovers.length !== 0) {
                approvers.push(
                  item.requisition.requisitionApprovers[0].approverId,
                );
              }

              // optimize this. instead of creating notification for each item.
              // create notification for all items at once.
              // move this outside the loop.
              await this.notificationService.sendNotification({
                title: NOTIFICATION_DETAILS.ITEM_SYNC.title,
                message: NOTIFICATION_DETAILS.ITEM_SYNC.message,
                type: NOTIFICATION_TYPES.REQUISITION_SLIP,
                recipientUserIds: [item.requisition.created_by, ...approvers],
                metaData: {
                  requisitionId: item.requisition.id,
                },
                senderId: userFromToken.id,
              });
            }

            // Scenario 3 RS status to assigned canvassing not empty status of draft/partial
            if (
              item.requisition.status === 'assigned' &&
              item.requisition.canvassRequisitions &&
              (item.requisition.canvassRequisitions.status ===
                CANVASS_STATUS.DRAFT ||
                CANVASS_STATUS.PARTIAL)
            ) {
              this.logToFile({
                message: `PROCESS_ITEM_SYNC: Scenario 3: Notifying Users, Assigned and Purchasing Head`,
              });
              // optimize this. instead of creating notification for each item.
              // create notification for all items at once.
              // move this outside the loop.
              await this.notificationService.sendNotification({
                title: NOTIFICATION_DETAILS.ITEM_SYNC.title,
                message: NOTIFICATION_DETAILS.ITEM_SYNC.message,
                type: NOTIFICATION_TYPES.REQUISITION_SLIP,
                recipientUserIds: [
                  item.requisition.created_by,
                  item.requisition.assigned_to,
                  purchasingHeadId,
                ],
                metaData: {
                  requisitionId: item.requisition.id,
                },
                senderId: userFromToken.id,
              });
            }

            //scenario 4 Have an Open RS with a Status of PO Created and PO Approval

            if (
              item.requisition.status === 'assigned' &&
              item.requisition.canvassRequisitions &&
              item.requisition.canvassRequisitions.status ===
              CANVASS_STATUS.APPROVED &&
              !item.requisition.purchaseOrders &&
              (item.requisition.purchaseOrders.status ===
                PO_STATUS.FOR_PO_REVIEW ||
                PO_STATUS.FOR_PO_APPROVAL)
            ) {
              this.logToFile({
                message: `PROCESS_ITEM_SYNC: Scenario 4: Notifying Users, Assigned and Purchasing Head`,
              });
              // optimize this. instead of creating notification for each item.
              // create notification for all items at once.
              // move this outside the loop.
              await this.notificationService.sendNotification({
                title: NOTIFICATION_DETAILS.ITEM_SYNC.title,
                message: NOTIFICATION_DETAILS.ITEM_SYNC.message,
                type: NOTIFICATION_TYPES.REQUISITION_SLIP,
                recipientUserIds: [
                  item.requisition.created_by,
                  item.requisition.assigned_to,
                  purchasingHeadId,
                ],
                metaData: {
                  requisitionId: item.requisition.id,
                },
                senderId: userFromToken.id,
              });
            }

            //scenario 5 Have an Open RS with a Status of For Delivery

            if (
              item.requisition.status === 'assigned' &&
              item.requisition.canvassRequisitions &&
              item.requisition.canvassRequisitions.status ===
              CANVASS_STATUS.APPROVED &&
              item.requisition.purchaseOrders &&
              item.requisition.purchaseOrders.status ===
              PO_STATUS.FOR_DELIVERY &&
              item.requisition.deliveryReceipt &&
              item.requisition.deliveryReceipt.latestDeliveryStatus !==
              'Fully Delivered'
            ) {
              this.logToFile({
                message: `PROCESS_ITEM_SYNC: Scenario 5: Notifying Users, Assigned and Purchasing Head`,
              });
              // optimize this. instead of creating notification for each item.
              // create notification for all items at once.
              // move this outside the loop.
              await this.notificationService.sendNotification({
                title: NOTIFICATION_DETAILS.ITEM_SYNC.title,
                message: NOTIFICATION_DETAILS.ITEM_SYNC.message,
                type: NOTIFICATION_TYPES.REQUISITION_SLIP,
                recipientUserIds: [
                  item.requisition.created_by,
                  item.requisition.assigned_to,
                  purchasingHeadId,
                ],
                metaData: {
                  requisitionId: item.requisition.id,
                },
                senderId: userFromToken.id,
              });
            }

            //scenario 6 Have an Open RS with a Status of For Payment Request and PR Approval

            if (
              item.requisition.status === 'assigned' &&
              item.requisition.canvassRequisitions &&
              item.requisition.canvassRequisitions.status ===
              CANVASS_STATUS.APPROVED &&
              item.requisition.purchaseOrders &&
              item.requisition.purchaseOrders.status ===
              PO_STATUS.FOR_DELIVERY &&
              item.requisition.deliveryReceipt &&
              item.requisition.deliveryReceipt.latestDeliveryStatus !==
              'Fully Delivered' &&
              item.requisition.rsPaymentRequest &&
              (item.requisition.rsPaymentRequest.status ===
                RS_PAYMENT_REQUEST_STATUS.SUBMITTED ||
                RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL)
            ) {
              this.logToFile({
                message: `PROCESS_ITEM_SYNC: Scenario 6: Notifying Users, Assigned and Purchasing Head`,
              });
              // optimize this. instead of creating notification for each item.
              // create notification for all items at once.
              // move this outside the loop.
              await this.notificationService.sendNotification({
                title: NOTIFICATION_DETAILS.ITEM_SYNC.title,
                message: NOTIFICATION_DETAILS.ITEM_SYNC.message,
                type: NOTIFICATION_TYPES.REQUISITION_SLIP,
                recipientUserIds: [
                  item.requisition.created_by,
                  item.requisition.assigned_to,
                  purchasingHeadId,
                ],
                metaData: {
                  requisitionId: item.requisition.id,
                },
                senderId: userFromToken.id,
              });
            }
          }
        }
      }

      this.logToFile({ message: `LOOP_ITEMS: ${items.length}` });

      for (const item of items) {
        this.logToFile({ message: `START_PROCESS_ITEM_SYNC` });

        if (uniqueItemCDs.has(item.itemcd ? item.itemcd : item.itemCd))
          continue;
        uniqueItemCDs.add(item.itemcd ? item.itemcd : item.itemCd);

        this.logToFile({
          message: `PROCESS_ITEM_SYNC: uniqueItemCDs: ${uniqueItemCDs.size}`,
        });
        this.logToFile({
          message: `PROCESS_ITEM_SYNC: item: ${JSON.stringify(item)}`,
        });

        const codes = this.extractCodesFromAcctCd(
          item.acctcd ? item.acctcd.trim() : item.acctCd.trim(),
        );
        if (!codes) continue;

        const combinationKey = `${codes.projectCode}-${codes.tradeCode}`;
        this.logToFile({
          message: `PROCESS_ITEM_SYNC: combinationKey: ${combinationKey}`,
        });

        if (!processedCombinations.has(combinationKey)) {
          try {
            const projectData =
              await this.ofmItemListRepository.findProjectWithCompany(
                codes.projectCode,
              );
            const tradeData = await this.ofmItemListRepository.findTradeByCode(
              codes.tradeCode,
            );

            if (projectData && tradeData) {
              const ofmListData = this.createOfmListData(
                projectData,
                tradeData,
                codes,
              );
              if (ofmListData) {
                ofmItemLists.push(ofmListData);
              }
            }
            processedCombinations.add(combinationKey);
          } catch (error) {
            this.logToFile({
              message: `PROCESS_ITEM_SYNC: Error processing combination: ${JSON.stringify(error)}`,
            });
            throw error;
          }
        }

        mappedItems.push(
          item.itemcd
            ? this.transformItemData(item)
            : { ...item, itemCd: item.acctCd, unit: '' },
        );
      }

      this.logToFile({ message: `MAPPED_ITEMS: ${mappedItems.length}` });
      const createdItems = await this.itemRepository.bulkCreate(mappedItems);

      let createdLists = [];
      if (ofmItemLists.length > 0) {
        this.logToFile({ message: `OFM_ITEM_LISTS: ${ofmItemLists.length}` });
        createdLists =
          await this.ofmItemListRepository.bulkCreateOfmLists(ofmItemLists);
      }

      if (createdLists.length > 0) {
        this.logToFile({ message: `LINKING_ITEMS_TO_LISTS` });
        await this.ofmItemListRepository.linkItemsToLists();
      }

      console.log({
        message: 'newCreatedItems',
        newCreatedItems,
      });

      return {
        createdItems,
        newCreatedItems
      };
    } catch (error) {
      this.logToFile({
        message: 'ERROR_PROCESSING_ITEMS',
        data: error,
      });
    }
  }

  async randomDelay(time = 100) {
    const delay = Math.floor(Math.random() * time) + 100;
    return new Promise((resolve) => setTimeout(resolve, delay));
  }
}

module.exports = ItemService;
