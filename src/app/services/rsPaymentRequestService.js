const { convertDateToDDMMMYYYY } = require('../utils');

class RsPaymentRequestService {
  constructor(container) {
    const {
      userRepository,
      roleRepository,
      requisitionRepository,
      rsPaymentRequestApproverRepository,
      purchaseOrderItemRepository,
      db,
      utils,
      entities,
      clientErrors,
      fastify,
      rsPaymentRequestRepository,
      commentRepository,
      constants,
      attachmentService,
      commentService,
      purchaseOrderService,
      deliveryReceiptService,
      purchaseOrderRepository,
      deliveryReceiptRepository,
      notificationService,
      deliveryReceiptInvoiceRepository,
      invoiceReportRepository,
      invoiceReportService,
      approverService,
      companyRepository,
      supplierRepository,
      projectRepository,
      citylandApiService,
    } = container;
    this.projectRepository = projectRepository;
    this.supplierRepository = supplierRepository;
    this.companyRepository = companyRepository;
    this.userRepository = userRepository;
    this.roleRepository = roleRepository;
    this.requisitionRepository = requisitionRepository;
    this.rsPaymentRequestApproverRepository =
      rsPaymentRequestApproverRepository;
    this.purchaseOrderItemRepository = purchaseOrderItemRepository;
    this.db = db;
    this.utils = utils;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.entities = entities;
    this.fastify = fastify;
    this.rsPaymentRequestRepository = rsPaymentRequestRepository;
    this.constants = constants;
    this.commentRepository = commentRepository;
    this.attachmentService = attachmentService;
    this.commentService = commentService;
    this.purchaseOrderService = purchaseOrderService;
    this.deliveryReceiptService = deliveryReceiptService;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.deliveryReceiptRepository = deliveryReceiptRepository;
    this.deliveryReceiptInvoiceRepository = deliveryReceiptInvoiceRepository;
    this.notificationService = notificationService;
    this.invoiceReportRepository = invoiceReportRepository;
    this.invoiceReportService = invoiceReportService;
    this.approverService = approverService;
    this.citylandApiService = citylandApiService;
  }

  async generatePRNumberCode(isDraft = false, transaction = null) {
    let whereClause;
    const numberField = isDraft === true ? 'prDraftNumber' : 'prNumber';
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

    if (isDraft === true) {
      whereClause = {
        status: RS_PAYMENT_REQUEST_STATUS.DRAFT,
        prDraftNumber: {
          [this.Sequelize.Op.ne]: null,
        },
      };
    } else {
      // FIXED: Use global numbering instead of status-specific numbering
      // This prevents duplicate numbers when VRs change from 'For PR Approval' to 'Closed'
      whereClause = {
        prNumber: {
          [this.Sequelize.Op.ne]: null,
        },
      };
    }

    const lastPr = await this.rsPaymentRequestRepository.findOne({
      where: whereClause,
      order: [[numberField, 'DESC']],
      transaction,
      lock: true,
    });

    const lastNumber = lastPr?.[numberField] ?? '0';
    const lastLetter = lastPr?.prLetter ?? 'AA';
    const parsedLastNumber = parseInt(lastNumber);
    const safeLastNumber = isNaN(parsedLastNumber) ? 0 : parsedLastNumber;
    const nextNumber =
      lastNumber === '99999999'
        ? '0'.padStart(8, '0')
        : (safeLastNumber + 1).toString().padStart(8, '0');
    const nextLetter =
      lastNumber === '99999999'
        ? this.utils.incrementLetters(lastLetter)
        : lastLetter;

    return {
      prNumber: nextNumber,
      prLetter: nextLetter,
    };
  }

  /**
   * Updates invoice associations for a payment request
   * @param {number} paymentRequestId - The ID of the payment request
   * @param {Array<number>} invoiceIds - Array of invoice IDs to associate
   * @param {Object} transaction - Sequelize transaction object
   */
  async updateInvoiceAssociations(paymentRequestId, invoiceIds, transaction) {
    // Get existing invoice IDs for this payment request
    const existingInvoices = await this.invoiceReportRepository.findAll({
      where: { paymentRequestId },
      attributes: ['id'],
      raw: true,
    });

    const existingInvoiceIds = existingInvoices.data.map(
      (invoice) => invoice.id,
    );

    const removedInvoiceIds = existingInvoiceIds.filter(
      (id) => !invoiceIds.includes(id),
    );

    const newInvoiceIds = invoiceIds.filter(
      (id) => !existingInvoiceIds.includes(id),
    );

    // Remove payment request ID from removed invoices
    if (removedInvoiceIds.length > 0) {
      await this.invoiceReportRepository.update(
        { id: { [this.Sequelize.Op.in]: removedInvoiceIds } },
        { paymentRequestId: null },
        { transaction },
      );
    }

    // Update payment request ID for new invoices
    if (newInvoiceIds.length > 0) {
      await this.invoiceReportRepository.update(
        { id: { [this.Sequelize.Op.in]: newInvoiceIds } },
        { paymentRequestId },
        { transaction },
      );
    }
  }

  async createRsPaymentRequest(request) {
    const { transaction, userFromToken, includesIncidentalFees, ...payload } =
      request;
    const numberField = payload.isDraft === true ? 'prDraftNumber' : 'prNumber';
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

    await this.invoiceReportService.validateInvoiceIds(
      payload.invoiceIds,
      payload.purchaseOrderId,
    );

    const { prLetter, prNumber } = await this.generatePRNumberCode(
      payload.isDraft,
      transaction,
    );

    // Get purchase order details
    const { termsData } = await this.getPurchaseOrderDetails({
      id: payload.purchaseOrderId,
      employeeId: payload?.termsData?.employeeId,
      userFromToken,
    });

    // Get invoice reports
    const invoiceReports = await this.invoiceReportRepository.findAll({
      where: { id: { [this.Sequelize.Op.in]: payload.invoiceIds } },
      attributes: ['invoiceAmount'],
      raw: true,
    });

    // Calculate base total from invoices
    let totalAmount = invoiceReports.data.reduce(
      (sum, report) => sum + Number(report.invoiceAmount),
      0,
    );

    // Add incidental fees if requested
    if (includesIncidentalFees === 'true') {
      const purchaseOrder = await this.purchaseOrderRepository.getById(
        payload.purchaseOrderId,
      );

      // Check if PO has incidental fees
      if (
        purchaseOrder.withholdingTaxDeduction ||
        purchaseOrder.deliveryFee ||
        purchaseOrder.tip ||
        purchaseOrder.extraCharges
      ) {
        // Calculate total incidental fees
        const totalIncidentalFees =
          Number(purchaseOrder.withholdingTaxDeduction || 0) +
          Number(purchaseOrder.deliveryFee || 0) +
          Number(purchaseOrder.tip || 0) +
          Number(purchaseOrder.extraCharges || 0);

        totalAmount += totalIncidentalFees;
      }
    }

    payload.totalAmount = Number(totalAmount.toFixed(2));

    const result = await this.rsPaymentRequestRepository.create(
      {
        ...payload,
        prLetter,
        termsData,
        includesIncidentalFees: includesIncidentalFees || false,
        [numberField]: prNumber,
        status:
          payload.isDraft === true
            ? RS_PAYMENT_REQUEST_STATUS.DRAFT
            : RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
      },
      transaction,
    );

    // Use the refactored method to update invoice associations
    await this.updateInvoiceAssociations(
      result.id,
      payload.invoiceIds,
      transaction,
    );

    // if (payload.isDraft === false) {
    //   await this.requisitionRepository.update(
    //     { id: payload.requisitionId },
    //     { status: RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL },
    //     { transaction },
    //   );
    // }

    const reloadedPaymentRequest =
      await this.rsPaymentRequestRepository.getById(result.id);

    return {
      ...reloadedPaymentRequest,
      invoiceIds: payload.invoiceIds,
    };
  }

  async createComment(request) {
    const { comment, id, userFromToken, transaction } = request;
    const { MODELS } = this.constants.attachment;

    this.fastify.log.info(`Adding comment to RS Payment Request`);
    this.fastify.log.info(`Checking if Payment Request is valid`);
    const paymentRequest = await this.rsPaymentRequestRepository.getById(id);

    if (!paymentRequest) {
      throw this.clientErrors.NOT_FOUND({
        message: `RS Payment Request with ID: ${id} not found`,
      });
    }

    const result = await this.commentService.createComment({
      transaction,
      model: MODELS.RS_PAYMENT_REQUEST,
      userId: userFromToken.id,
      comment: comment,
      modelId: id,
    });

    this.fastify.log.info(
      `Successfully added Comment RS Payment Request with ID: ${id}`,
    );

    return result;
  }

  async createAttachment(request) {
    const { transaction, attachments, id, userFromToken } = request;
    const { MODELS } = this.constants.attachment;

    this.fastify.log.info(`Adding Attachments to RS Payment Request`);
    this.fastify.log.info(`Checking if Payment Request is valid`);
    const paymentRequest = await this.rsPaymentRequestRepository.getById(id);

    if (!paymentRequest) {
      throw this.clientErrors.NOT_FOUND({
        message: `RS Payment Request with ID: ${id} not found`,
      });
    }

    const result = await this.attachmentService.createAttachments({
      attachments,
      transaction,
      model: MODELS.RS_PAYMENT_REQUEST,
      parentPath: MODELS.RS_PAYMENT_REQUEST,
      userId: userFromToken.id,
      modelId: id,
    });

    this.fastify.log.info(
      `Successfully added Attachment/s RS Payment Request with ID: ${id}`,
    );

    return result;
  }

  async getPurchaseOrderDetails(request) {
    const { id, userFromToken, employeeId } = request;
    const purchaseOrderDetails =
      await this.purchaseOrderService.getPODetailsSupplierWarranty(id);

    if (!purchaseOrderDetails) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order with ID: ${id} not found`,
      });
    }

    const deliveryReceipt =
      await this.deliveryReceiptService.getDeliveryReceiptByPOId(id);

    let deliveryInvoice = null;
    let drDetails = null;

    if (deliveryReceipt) {
      deliveryInvoice = await this.deliveryReceiptInvoiceRepository.findOne({
        where: { deliveryReceiptId: deliveryReceipt.id },
      });

      if (deliveryInvoice) {
        drDetails = await this.deliveryReceiptRepository.getById(
          deliveryInvoice.deliveryReceiptId,
        );
      }
    } else {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order is not Associated with Delivery Receipt`,
      });
    }

    const purchaseOrderAmountAgainstPaymentRequestMonitoring =
      await this.purchaseOrderService.getPurchaseOrderAgainstPaymentRequestMonitoring(
        id,
      );

    const terms = await this.utils.Terms({
      terms: purchaseOrderDetails.terms,
      employeeId,
      totalAmount: deliveryReceipt.invoice?.totalSales,
      deposit: purchaseOrderDetails?.depositPercent,
      issuedInvoiceDate: deliveryReceipt.issuedInvoiceDate,
      userFromToken,
    });

    // Add incidental fees information to the response
    const incidentalFeesStatus = await this.getIncidentalFeesStatus(id);

    return {
      ...purchaseOrderDetails,
      termsData: terms,
      deliveryInvoice: deliveryInvoice || null,
      invoiceAttachment: drDetails?.invoice?.invoiceAttachment || null,
      poAmountAndPrAmountMonitoring:
        purchaseOrderAmountAgainstPaymentRequestMonitoring,
      incidentalFees: incidentalFeesStatus,
    };
  }

  async getPaymentRequestById(id) {
    const paymentRequest =
      await this.rsPaymentRequestRepository.getPaymentRequestById(id);

    if (!paymentRequest) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Payment request not found',
      });
    }

    const employeeId = parseInt(paymentRequest?.termsData?.employeeId);
    if (employeeId) {
      const employee = await this.userRepository.getById(employeeId, {
        paranoid: false,
        attributes: ['id', 'firstName', 'lastName'],
      });

      paymentRequest.employee = employee;
    }

    return paymentRequest;
  }

  async getPRItems(purchaseOrderId, search, sort = {}, queries = {}, type) {
    const prItems =
      await this.purchaseOrderItemRepository.getItemsForPaymentRequest({
        ...queries,
        search,
        paginate: false,
        sort: sort || {},
        purchaseOrderId,
        type,
      });

    return prItems;
  }

  async getExistingPR(id, transaction = null) {
    const existingPR = await this.rsPaymentRequestRepository.getById(id);

    if (!existingPR) {
      throw this.clientErrors.NOT_FOUND({
        message: `Payment request with id ${id} does not exist`,
      });
    }

    return existingPR;
  }

  async submit(request) {
    this.fastify.log.info(`Submitting Payment Request...`);
    const { userFromToken, body, transaction } = request;
    const { attachments, ...details } = { ...body };
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;
    let newBody = { ...details };
    let paymentRequestId;
    let validatePr;

    this.fastify.log.info(`Checking if Payment Request new or Draft`);

    if (details.id) {
      this.fastify.log.info(`Checking if Payment Request is a Draft`);
      const { id, ...newDetails } = { ...details };
      newBody = newDetails;
      paymentRequestId = id;
    }

    const parsedDetails = this.utils.parseDomain(
      this.entities.rsPaymentRequest.submitRsPaymentRequestSchema,
      newBody,
    );

    if (paymentRequestId) {
      validatePr = await this.rsPaymentRequestRepository.findOne({
        where: {
          id: paymentRequestId,
        },
      });
    }

    // no payment request found create a submitted payment request
    if (!validatePr) {
      let attachment;
      let comment;

      const paymentRequest = await this.createRsPaymentRequest({
        ...parsedDetails,
        transaction,
      });

      if (parsedDetails.comments) {
        comment = await this.createComment({
          id: paymentRequest.id,
          comment: parsedDetails.comments,
          userFromToken,
          transaction,
        });
      }

      if (attachments?.length) {
        attachment = await this.createAttachment({
          transaction,
          userFromToken,
          attachments,
          id: paymentRequest.id,
        });
      }

      this.fastify.log.info(`Generating PR Approvers - Create PR flow`);
      await this.#generatePRApprovers({
        transaction,
        paymentRequest,
      });

      await transaction.commit();

      this.fastify.log.info(`Created RS Payment Request Successfully`);
      return { paymentRequest, attachment, comment };
    }

    // if payment request found and status of draft or rejected
    if (
      validatePr &&
      validatePr.status === RS_PAYMENT_REQUEST_STATUS.SUBMITTED
    ) {
      throw this.clientErrors.NOT_FOUND({
        message: `Payment request with ID of ${paymentRequestId} is already submitted.`,
      });
    }

    this.fastify.log.info(`Payment Request is a draft. Submitting...`);
    const { prLetter, prNumber } = await this.generatePRNumberCode(
      parsedDetails.isDraft,
      transaction,
    );

    let result;

    const numberField =
      parsedDetails.isDraft === true ? 'prDraftNumber' : 'prNumber';
    this.fastify.log.info(`Updating submitted payment request`);
    result = await this.rsPaymentRequestRepository.update(
      { id: paymentRequestId },
      {
        ...parsedDetails,
        status:
          parsedDetails.isDraft === true
            ? RS_PAYMENT_REQUEST_STATUS.DRAFT
            : RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
        prLetter,
        [numberField]: prNumber,
      },
      { transaction },
    );

    // Use the refactored method to update invoice associations
    await this.updateInvoiceAssociations(
      paymentRequestId,
      parsedDetails.invoiceIds,
      transaction,
    );

    if (parsedDetails.isDraft === false) {
      this.fastify.log.info(`Generating payment request approvers`);
      await this.#generatePRApprovers({
        transaction,
        paymentRequest: validatePr,
      });
    }

    return { paymentRequest: result[1][0].dataValues };
  }

  async approvePurchaseRequest(payload = {}) {
    const {
      existingPaymentRequest,
      userFromToken,
      transaction = null,
    } = payload;
    const { PR_APPROVER_STATUS, RS_PAYMENT_REQUEST_STATUS } =
      this.constants.rsPaymentRequest;
    const { USER_TYPES } = this.constants.user;

    if (existingPaymentRequest.status === RS_PAYMENT_REQUEST_STATUS.DRAFT) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Payment Request is in draft status`,
      });
    }

    const approvers = await this.rsPaymentRequestApproverRepository.getPRApprovers(existingPaymentRequest.id);

    const userApproverRecords = approvers.data.filter((approverRecord) => {
        const prAltApproverId = approverRecord.approver.userLeaves.length
        ? approverRecord.approver.userLeaves[0].prAltUser.id
        : 0;

      const isApprover =
        approverRecord.userId === userFromToken.id ||
        approverRecord.altApproverId === userFromToken.id ||
        prAltApproverId === userFromToken.id;
      return isApprover;
    });

    if (userApproverRecords.length === 0) {
      throw this.clientErrors.FORBIDDEN({
        message: `You are not authorized to approve this payment request`,
      });
    }

    const pendingApprovals = userApproverRecords
      .filter((record) => record.status === PR_APPROVER_STATUS.PENDING)
      .sort((a, b) => a.level - b.level);

    const currentApprover =
      pendingApprovals.length > 0 ? pendingApprovals[0] : null;

    console.log('------------------------------------- ', currentApprover);

    if (!currentApprover) {
      throw this.clientErrors.BAD_REQUEST({
        message: `You have no pending approvals for this payment request`,
      });
    }

    await this.approverService.overrideApprover({
      model: 'rsPaymentRequest',
      modelId: existingPaymentRequest.id,
      approverId: userFromToken.id,
      status: 'approved',
      transaction,
      requisitionId: existingPaymentRequest.requisitionId,
    });

    await this.rsPaymentRequestApproverRepository.update(
      { id: currentApprover.id },
      { status: PR_APPROVER_STATUS.APPROVED },
      { transaction },
    );

    const { data: updatedApprovers } =
      await this.rsPaymentRequestApproverRepository.findAll({
        where: { paymentRequestId: existingPaymentRequest.id },
        transaction,
      });

    const allApproved = updatedApprovers.every(
      (approver) => approver.status === PR_APPROVER_STATUS.APPROVED,
    );

    if (allApproved) {
      await this.rsPaymentRequestRepository.update(
        { id: existingPaymentRequest.id },
        { status: RS_PAYMENT_REQUEST_STATUS.APPROVED },
        { transaction },
      );

      await this.sendApprovedPaymentRequestToAccounting(
        existingPaymentRequest,
        userFromToken,
      );
    }
    return allApproved;
  }

  async generateAccountingPayload(paymentRequest, userFromToken) {
    try {
      // Get purchase order details for the payment request
      const purchaseOrder = await this.purchaseOrderRepository.findOne({
        where: { id: paymentRequest.purchaseOrderId },
        include: [
          {
            model: this.db.requisitionModel,
            as: 'requisition',
            attributes: ['companyCode', 'projectId', 'type'],
            include: [
              {
                model: this.db.projectModel,
                as: 'project',
                attributes: ['code'],
              },
            ],
          },
        ],
      });

      if (!purchaseOrder) {
        throw new Error(
          `Purchase order not found for payment request ID: ${paymentRequest.id}`,
        );
      }

      // Get supplier details based on supplier type
      let supplier;
      if (purchaseOrder.supplierType === 'supplier') {
        supplier = await this.supplierRepository.findOne({
          where: { id: purchaseOrder.supplierId },
          attributes: ['id', ['pay_code', 'code'], 'name', 'tin'],
        });
      } else if (purchaseOrder.supplierType === 'company') {
        supplier = await this.companyRepository.findOne({
          where: { id: purchaseOrder.supplierId },
          attributes: ['id', ['initial', 'code'], 'name', 'tin'],
        });
      } else if (purchaseOrder.supplierType === 'project') {
        supplier = await this.projectRepository.findOne({
          where: { id: purchaseOrder.supplierId },
          attributes: ['id', ['initial', 'code'], 'name'],
        });
      }

      if (!supplier) {
        throw new Error(
          `Supplier not found for purchase order ID: ${purchaseOrder.id}`,
        );
      }

      // Get invoice IDs associated with this payment request
      const invoiceIds = await this.invoiceReportRepository.findAll({
        where: { paymentRequestId: paymentRequest.id },
        attributes: ['id'],
        raw: true,
        paginate: false,
      });

      const invoiceIdsArray = invoiceIds.data.map((invoice) => invoice.id);

      if (invoiceIdsArray.length === 0) {
        throw new Error(
          `No invoices found for payment request ID: ${paymentRequest.id}`,
        );
      }

      // Get the total invoice amount directly from the invoice_reports table
      const [invoiceTotalResult] = await this.db.sequelize.query(`
        SELECT SUM(invoice_amount) as total_invoice_amount
        FROM invoice_reports
        WHERE id IN (${invoiceIdsArray.join(',')})
      `);

      const totalInvoiceAmount =
        invoiceTotalResult[0]?.total_invoice_amount || 0;

      // Use raw SQL query to get all the necessary data in one go
      const invoiceItemsQuery = `
        SELECT 
          ir.id as invoice_id,
          ir.invoice_amount,
          dr.id as delivery_receipt_id,
          dri.id as delivery_receipt_item_id,
          dri.qty_delivered,
          poi.id as purchase_order_item_id,
          ril.item_type,
          CASE 
            WHEN ril.item_type IN ('ofm', 'ofm-tom') THEN i.acct_cd
            ELSE ni.acct_cd
          END as account_code,
          CASE 
            WHEN ril.item_type IN ('ofm', 'ofm-tom') THEN i.itm_des
            ELSE ni.item_name
          END as item_description,
          CASE 
            WHEN ril.item_type IN ('ofm', 'ofm-tom') THEN i.unit
            ELSE ni.unit
          END as item_unit,
          CASE 
            WHEN cis.discount_type = 'fixed' THEN cis.unit_price - cis.discount_value
            WHEN cis.discount_type = 'percent' THEN cis.unit_price * (1 - cis.discount_value / 100)
            ELSE cis.unit_price
          END as unit_price
        FROM 
          invoice_reports ir
        JOIN 
          delivery_receipts dr ON dr.invoice_id = ir.id
        JOIN 
          delivery_receipt_items dri ON dri.dr_id = dr.id
        JOIN 
          purchase_order_items poi ON dri.po_item_id = poi.id
        JOIN 
          requisition_item_lists ril ON poi.requisition_item_list_id = ril.id
        JOIN 
          canvass_item_suppliers cis ON poi.canvass_item_supplier_id = cis.id
        LEFT JOIN 
          items i ON ril.item_type IN ('ofm', 'ofm-tom') AND ril.item_id = i.id
        LEFT JOIN 
          non_ofm_items ni ON ril.item_type NOT IN ('ofm', 'ofm-tom') AND ril.item_id = ni.id
        WHERE 
          ir.id IN (${invoiceIdsArray.join(',')})
      `;

      const [invoiceItems] = await this.db.sequelize.query(invoiceItemsQuery);

      // Format the data for accounting system
      return this.formatPaymentRequestForAccountingFromRaw(
        paymentRequest,
        purchaseOrder,
        supplier,
        invoiceItems,
        userFromToken,
        totalInvoiceAmount,
      );
    } catch (error) {
      this.fastify.log.error(
        `Failed to generate accounting payload: ${error.message}`,
      );
      throw error;
    }
  }

  async sendApprovedPaymentRequestToAccounting(paymentRequest, userFromToken) {
    try {
      this.fastify.log.info(
        `Sending approved payment request to accounting system: ${paymentRequest.id}`,
      );

      // Generate the payload
      const formattedData = await this.generateAccountingPayload(
        paymentRequest,
        userFromToken,
      );

      // Send to accounting system
      const response = await this.citylandApiService.post(
        '/payment_processing/voucher_request/',
        formattedData,
      );

      this.fastify.log.info(
        `Successfully sent payment request to accounting system. Response: ${JSON.stringify(response)}`,
      );
      return response;
    } catch (error) {
      this.fastify.log.error(
        `Failed to send payment request to accounting: ${error.message}`,
      );
      // Don't throw error to prevent blocking the approval process
      return null;
    }
  }

  formatPaymentRequestForAccountingFromRaw(
    paymentRequest,
    purchaseOrder,
    supplier,
    invoiceItems,
    userFromToken,
    totalInvoiceAmount,
  ) {
    // Group items by account code
    const itemsByAccountCode = {};

    invoiceItems.forEach((item) => {
      const accountCode = item.account_code;
      if (!accountCode) return;

      if (!itemsByAccountCode[accountCode]) {
        itemsByAccountCode[accountCode] = {
          account_code: accountCode,
          amount: 0,
          items: [],
        };
      }

      // Calculate item amount based on quantity and unit price
      const itemAmount =
        Number(item.qty_delivered || 0) * Number(item.unit_price || 0);

      // Check if this item already exists in the items array
      const existingItemIndex = itemsByAccountCode[accountCode].items.findIndex(
        (existingItem) =>
          existingItem.description === item.item_description &&
          existingItem.unit === item.item_unit,
      );

      if (existingItemIndex >= 0) {
        // Update existing item
        const existingItem =
          itemsByAccountCode[accountCode].items[existingItemIndex];
        existingItem.quantity += Number(item.qty_delivered || 0);
        existingItem.amount += itemAmount;
      } else {
        // Add new item
        itemsByAccountCode[accountCode].items.push({
          quantity: Number(item.qty_delivered || 0),
          unit: item.item_unit,
          description: item.item_description,
          amount: itemAmount,
        });
      }

      // Update total amount for this account code
      itemsByAccountCode[accountCode].amount += itemAmount;
    });

    // Format PR number
    const prNumber = `VR${paymentRequest.prLetter}${paymentRequest.prNumber}`;

    // Get date and time from payment request creation
    const createdAt = new Date(paymentRequest.createdAt);
    const datePrepared = createdAt.toISOString().split('T')[0];
    const timePrepared = createdAt.toTimeString().split(' ')[0];

    // Round amounts to 2 decimal places
    Object.values(itemsByAccountCode).forEach((accountLine) => {
      accountLine.amount = Number(accountLine.amount.toFixed(2));
      accountLine.items.forEach((item) => {
        item.amount = Number(item.amount.toFixed(2));
      });
    });

    // Prepare supplier data with fallbacks for missing fields
    const supplierData = {
      code: supplier.code || '',
      name: supplier.name || '',
      tin: supplier.tin || '',
    };

    // Use the calculated total from invoices instead of the payment request's totalAmount
    const calculatedTotal = Number(totalInvoiceAmount);

    return {
      voucher_request: {
        prs_username: userFromToken.username,
        voucher_request_no: prNumber,
        branch_code: purchaseOrder.requisition.companyCode || '1',
        company_code: purchaseOrder.requisition.companyCode || '1',
        date_prepared: datePrepared,
        time_prepared: timePrepared,
        supplier: supplierData,
        total_amount: calculatedTotal,
        project_code: purchaseOrder.requisition.project?.code || '',
        lines: Object.values(itemsByAccountCode),
      },
    };
  }

  async #generatePRApprovers(payload) {
    const { USER_TYPES } = this.constants.user;
    const { paymentRequest, transaction } = payload;
    const { PR_APPROVER_STATUS } = this.constants.rsPaymentRequest;

    /* Check existing approvers and handle resubmission case */
    const approvers = await this.rsPaymentRequestApproverRepository.findAll({
      paginate: false,
      where: { paymentRequestId: paymentRequest.id },
    });

    if (approvers.total > 0) {
      const hasRejectedApprovers = approvers.data.some(
        (approver) => approver.status === PR_APPROVER_STATUS.REJECTED,
      );

      if (hasRejectedApprovers) {
        await this.rsPaymentRequestApproverRepository.update(
          {
            paymentRequestId: paymentRequest.id,
            status: PR_APPROVER_STATUS.REJECTED,
          },
          { status: PR_APPROVER_STATUS.PENDING },
          { transaction },
        );
      }
      return;
    }

    const [requisition, supervisorRole, purchasingHeadRole] = await Promise.all(
      [
        this.requisitionRepository.findOne({
          where: { id: paymentRequest.requisitionId },
        }),
        this.roleRepository.findOne({
          where: { name: USER_TYPES.SUPERVISOR },
        }),
        this.roleRepository.findOne({
          where: { name: USER_TYPES.PURCHASING_HEAD },
        }),
      ],
    );

    if (!(supervisorRole && purchasingHeadRole)) {
      throw this.clientErrors.BAD_REQUEST({
        message:
          'Required approver roles for the payment request management are not set',
      });
    }

    const [assignedUser, userPurchaseHead] = await Promise.all([
      this.userRepository.getUserById(requisition.assignedTo, {
        paranoid: false,
      }),
      this.userRepository.findOne({
        where: { roleId: purchasingHeadRole.id },
        paranoid: false,
      }),
    ]);

    let prApprovers;
    const isOFM = ['ofm', 'ofm-tom'].includes(requisition?.type);
    if (isOFM) {
      const requester = await this.userRepository.getUserById(
        requisition.createdBy,
        { paranoid: false },
      );

      prApprovers = [
        {
          level: 1,
          roleId: supervisorRole.id,
          ...(assignedUser?.supervisor?.id && {
            userId: assignedUser.supervisor.id,
          }),
        },
        {
          level: 2,
          roleId: supervisorRole.id,
          ...(requester?.supervisor?.id && {
            userId: requester.supervisor.id,
          }),
        },
        {
          level: 3,
          roleId: purchasingHeadRole.id,
          ...(userPurchaseHead?.id && {
            userId: userPurchaseHead.id,
          }),
        },
      ];
    } else {
      prApprovers = [
        {
          level: 1,
          roleId: supervisorRole.id,
          ...(assignedUser?.supervisor?.id && {
            userId: assignedUser.supervisor.id,
          }),
        },
        {
          level: 2,
          roleId: purchasingHeadRole.id,
          ...(userPurchaseHead?.id && {
            userId: userPurchaseHead.id,
          }),
        },
      ];
    }

    if (prApprovers.length > 0) {
      await this.rsPaymentRequestApproverRepository.bulkCreate(
        prApprovers.map((approver) => ({
          ...approver,
          paymentRequestId: paymentRequest.id,
        })),
        { transaction },
      );
    }
  }

  async getPOlists(requisitionId) {
    this.fastify.log.info(`Retrieving PO lists...`);
    const { PO_STATUS } = this.constants.purchaseOrder;
    const { STATUSES } = this.constants.deliveryReceipt;

    const requisition = await this.requisitionRepository.findOne({
      attributes: ['companyCode'],
      where: {
        id: Number(requisitionId),
      },
    });

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id of ${requisitionId} not found`,
      });
    }

    const poList = await this.purchaseOrderRepository.findAll({
      attributes: [
        'id',
        [
          this.db.Sequelize.fn(
            'CONCAT',
            'PO-',
            this.db.Sequelize.col('requisition.company_code'),
            this.db.Sequelize.col('po_letter'),
            this.db.Sequelize.col('po_number'),
          ),
          'poNumber',
        ],
      ],
      where: {
        requisitionId,
        status: PO_STATUS.FOR_DELIVERY,
        wasCancelled: false,
        [this.db.Sequelize.Op.and]: [
          this.db.Sequelize.literal(`
          EXISTS (
	          SELECT 1 FROM invoice_reports ir
	          WHERE ir.requisition_id = "purchase_orders"."requisition_id"
            AND ir.purchase_order_id = "purchase_orders"."id"
	          AND ir.payment_request_id is NULL
          )
          `),
          this.db.Sequelize.literal(`
          EXISTS (
	          SELECT 1 FROM delivery_receipts dr
	          WHERE dr.po_id = "purchase_orders"."id"
	          AND dr.status = '${STATUSES.DELIVERED}'
          )
          `),
        ],
      },
      include: [
        {
          association: 'requisition',
          attributes: [],
        },
      ],
      order: [['id', 'ASC']],
    });

    this.fastify.log.info(`Retrieved PO lists`);
    return poList;
  }

  async addAdhocApprover(payload = {}) {
    const {
      paymentRequestId,
      approver,
      creatorId,
      transaction,
      requisitionId,
      fullName,
    } = payload;
    const { PR_APPROVER_STATUS } = this.constants.rsPaymentRequest;
    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;

    const approvers = await this.rsPaymentRequestApproverRepository.findAll({
      paginate: false,
      where: {
        userId: creatorId,
        paymentRequestId,
        status: PR_APPROVER_STATUS.PENDING,
      },
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    if (approvers.total === 0) {
      throw this.clientErrors.FORBIDDEN({
        message: 'Only existing approvers can add adhoc approvers',
      });
    }

    const creatorApprover = approvers.data[0];

    if (creatorApprover.isAdhoc) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Adhoc approvers cannot add other adhoc approvers',
      });
    }

    const isExistingApprover =
      await this.rsPaymentRequestApproverRepository.findOne({
        where: {
          paymentRequestId,
          userId: approver.id,
        },
      });

    if (isExistingApprover) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Approver already exists',
      });
    }

    // Get creator user details for override metadata
    const creatorUser = await this.userRepository.getUserById(creatorId);

    const existingAdhoc = await this.rsPaymentRequestApproverRepository.findOne(
      {
        where: {
          paymentRequestId,
          level: creatorApprover.level,
          isAdhoc: true,
        },
      },
    );

    if (existingAdhoc) {
      await this.rsPaymentRequestApproverRepository.update(
        { id: existingAdhoc.id },
        {
          userId: approver.id,
          addedBy: creatorId,
        },
        { transaction },
      );

      return;
    }

    // Create the adhoc approver
    await this.rsPaymentRequestApproverRepository.create(
      {
        paymentRequestId,
        userId: approver.id,
        level: creatorApprover.level,
        isAdhoc: true,
        roleId: approver.role.id,
        addedBy: creatorId,
      },
      { transaction },
    );

    await this.notificationService.sendNotification({
      transaction,
      senderId: creatorId,
      type: NOTIFICATION_TYPES.PAYMENT_REQUEST,
      title: NOTIFICATION_DETAILS.ADDITIONAL_APPROVER_PR.title,
      message: NOTIFICATION_DETAILS.ADDITIONAL_APPROVER_PR.message(fullName),
      recipientUserIds: [approver.id],
      metaData: {
        addedBy: creatorId,
        adhocApprover: approver.id,
        paymentRequestId,
        requisitionId,
      },
    });
  }

  async rejectPaymentRequest(payload = {}) {
    const { existingPaymentRequest, approverId, userFromToken, transaction } =
      payload;
    const { PR_APPROVER_STATUS, RS_PAYMENT_REQUEST_STATUS } =
      this.constants.rsPaymentRequest;
    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;
    const { USER_TYPES } = this.constants.user;

    const approvers = await this.rsPaymentRequestApproverRepository.findAll({
      paginate: false,
      where: {
        paymentRequestId: existingPaymentRequest.id,
      },
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    const userApproverRecords = approvers.data.filter((approverRecord) => {
      const isApprover =
        approverRecord.userId === approverId ||
        approverRecord.altApproverId === approverId;

      return isApprover;
    });

    if (userApproverRecords.length === 0) {
      throw this.clientErrors.FORBIDDEN({
        message: `You are not authorized to approve this payment request`,
      });
    }

    const pendingApprovals = userApproverRecords
      .filter((record) => record.status === PR_APPROVER_STATUS.PENDING)
      .sort((a, b) => a.level - b.level);

    const currentApprover =
      pendingApprovals.length > 0 ? pendingApprovals[0] : null;

    if (!currentApprover) {
      throw this.clientErrors.BAD_REQUEST({
        message: `You have no pending approvals for this payment request`,
      });
    }

    await this.approverService.overrideApprover({
      model: 'rsPaymentRequest',
      modelId: existingPaymentRequest.id,
      approverId: userFromToken.id,
      status: 'rejected',
      transaction,
      requisitionId: existingPaymentRequest.requisitionId,
    });

    await this.rsPaymentRequestApproverRepository.update(
      { id: currentApprover.id },
      { status: PR_APPROVER_STATUS.REJECTED },
      { transaction },
    );

    await this.rsPaymentRequestRepository.update(
      { id: existingPaymentRequest.id },
      { status: RS_PAYMENT_REQUEST_STATUS.REJECTED },
      { transaction },
    );

    // Get the purchase order to format the PR number
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: { id: existingPaymentRequest.purchaseOrderId },
      include: [
        {
          model: this.db.requisitionModel,
          as: 'requisition',
          attributes: ['companyCode', 'createdBy', 'assignedTo'],
        },
      ],
    });

    if (purchaseOrder) {
      // Format the PR number
      const prNumber = `VR-${existingPaymentRequest.prLetter}${existingPaymentRequest.prNumber}`;

      // Send notification to the requester and assigned purchasing staff
      await this.notificationService.sendNotification({
        transaction,
        senderId: approverId,
        type: NOTIFICATION_TYPES.PAYMENT_REQUEST,
        title: NOTIFICATION_DETAILS.REJECT_PAYMENT_REQUEST.title,
        message: NOTIFICATION_DETAILS.REJECT_PAYMENT_REQUEST.message(prNumber),
        recipientUserIds: [
          purchaseOrder.requisition.assignedTo,
          purchaseOrder.requisition.createdBy,
        ],
        metaData: {
          rejectedBy: approverId,
          paymentRequestId: existingPaymentRequest.id,
          requisitionId: existingPaymentRequest.requisitionId,
        },
      });
    }
  }

  async cascadeRoleApprover({ userId, roleId, transaction }) {
    const { USER_TYPES } = this.constants.user;
    const { PR_APPROVER_STATUS } = this.constants.rsPaymentRequest;
    const options = transaction ? { transaction } : {};

    const role = await this.roleRepository.findOne({ where: { id: roleId } });
    const isPurchasingHead = role?.name === USER_TYPES.PURCHASING_HEAD;

    if (!isPurchasingHead) {
      return;
    }

    const pendingApprovers =
      await this.rsPaymentRequestApproverRepository.findAll({
        paginate: false,
        where: {
          roleId,
          isAdhoc: false,
          status: PR_APPROVER_STATUS.PENDING,
          userId: null,
          level: {
            [this.db.Sequelize.Op.in]: [2, 3],
          },
        },
      });

    if (pendingApprovers.total === 0) {
      this.fastify.log.info(
        `[INFO] No pending ${role.name} approvers found to cascade`,
      );
      return;
    }

    try {
      const updatePromises = pendingApprovers.data.map((approver) => {
        return this.rsPaymentRequestApproverRepository.update(
          { id: approver.id },
          { userId },
          options,
        );
      });

      await Promise.all(updatePromises);
      this.fastify.log.info(
        `[INFO] Successfully cascaded ${pendingApprovers.total} ${role.name} approvers.
        Payment Request Approver IDs: ${pendingApprovers.data.map((a) => a.id).join(', ')}`,
      );
    } catch (error) {
      this.fastify.log.error(
        `[ERROR] Failed to cascade ${role.name} approver in payment request`,
      );
      this.fastify.log.error(error);
      throw error;
    }
  }

  async cascadeSupervisorId({ transaction, userId, supervisorId }) {
    const approvers =
      await this.rsPaymentRequestApproverRepository.findPendingApproversByUserId(
        userId,
      );

    this.fastify.log.info(
      `[PR-Cascade] Pending approvers for user ID ${userId}: ${JSON.stringify(approvers)}`,
    );

    if (approvers.total === 0) {
      this.fastify.log.info(
        `[PR-Cascade] No pending PR Request associated with user ID ${userId}`,
      );
      return;
    }

    const updatePromises = approvers.data.map((approver) =>
      this.rsPaymentRequestApproverRepository.update(
        { id: approver.id },
        { userId: supervisorId },
        { transaction },
      ),
    );

    await Promise.all(updatePromises);

    this.fastify.log.info(
      `[PR-Cascade] Successfully cascaded supervisor ID ${supervisorId} for user ID ${userId}`,
    );
  }

  async getPaymentRequestsFromRequisitionId(
    requisitionId,
    { search, page, limit, sortBy },
  ) {
    const paymentRequests =
      await this.rsPaymentRequestRepository.getPaymentRequestsFromRequisitionId(
        requisitionId,
        { search, page, limit, sortBy },
      );

    return this._formatPaymentRequestForListing(paymentRequests);
  }

  _formatPaymentRequestForListing(paymentRequests) {
    return {
      data: paymentRequests.data?.map((paymentRequest) => ({
        id: paymentRequest.id,
        prNumber: paymentRequest.prNumber,
        lastApprover: paymentRequest.lastApprover
          ? `${paymentRequest.lastApprover.firstName} ${paymentRequest.lastApprover.lastName}`
          : null,
        lastUpdate: convertDateToDDMMMYYYY(paymentRequest.updatedAt),
        status: paymentRequest.status,
      })),
      total: paymentRequests.total,
    };
  }

  async getRsPaymentRequestByPk(id, transaction = null) {
    const rsPaymentRequest = await this.rsPaymentRequestRepository.findOne(
      {
        where: { id },
        include: [
          {
            model: this.db.purchaseOrderModel,
            include: [
              {
                model: this.db.supplierModel,
                attributes: ['name'],
                as: 'supplier',
              },
            ],
            as: 'purchaseOrder',
          },
          {
            model: this.db.requisitionModel,
            include: [
              {
                model: this.db.companyModel,
                attributes: ['id', 'name'],
                as: 'company',
              },
            ],
            as: 'requisition',
          },
        ],
      },
      { transaction },
    );

    if (!rsPaymentRequest) {
      throw this.clientErrors.NOT_FOUND({
        message: `Payment request not found with ID ${id}`,
      });
    }

    const items = await this.purchaseOrderItemRepository.getPOItemsById({
      purchaseOrderId: rsPaymentRequest.purchaseOrderId,
      paginate: false,
      transaction,
    });

    if (!items) {
      throw this.clientErrors.NOT_FOUND({
        message: `No Purchase Order Items found in Payment Request: ${id}`,
      });
    }

    return { rsPaymentRequest, items };
  }

  async removeAdhocApprover(payload = {}) {
    const { id, primaryApproverId } = payload;
    const { PR_APPROVER_STATUS } = this.constants.rsPaymentRequest;

    const approvers =
      await this.rsPaymentRequestApproverRepository.getPRApprovers(id);

    const primaryApprover = approvers.data.find(
      (approver) => approver.userId === primaryApproverId && !approver.isAdhoc,
    );

    if (!primaryApprover) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You are not authorized to remove adhoc approvers',
      });
    }

    const adhocApprover = approvers.data.find(
      (approver) =>
        approver.isAdhoc && approver.level === primaryApprover.level,
    );

    if (!adhocApprover) {
      throw this.clientErrors.NOT_FOUND({
        message: `No adhoc approver found for level ${primaryApprover.level}`,
      });
    }

    await this.rsPaymentRequestApproverRepository.destroy({
      id: adhocApprover.id,
    });
  }

  async resubmitRejectedPaymentRequest(payload = {}) {
    const { existingPaymentRequest, transaction, userFromToken, body } =
      payload;
    const { PR_APPROVER_STATUS, RS_PAYMENT_REQUEST_STATUS } =
      this.constants.rsPaymentRequest;
    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;

    const isValidForResubmission =
      existingPaymentRequest.status === RS_PAYMENT_REQUEST_STATUS.REJECTED;

    if (!isValidForResubmission) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Payment request is not ready for resubmission.`,
      });
    }

    // Get the associated purchase order to check authorization
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: { id: existingPaymentRequest.purchaseOrderId },
      include: [
        {
          model: this.db.requisitionModel,
          as: 'requisition',
          attributes: ['assignedTo', 'companyCode'],
        },
      ],
    });

    if (!purchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase order with ID ${existingPaymentRequest.purchaseOrderId} not found`,
      });
    }

    // Check if the current user is the assigned user for the requisition
    if (purchaseOrder.requisition.assignedTo !== userFromToken.id) {
      throw this.clientErrors.FORBIDDEN({
        message: `You are not authorized to resubmit this payment request. Only the assigned purchasing staff can resubmit.`,
      });
    }

    this.fastify.log.info(
      `Resubmitting rejected payment request with ID: ${existingPaymentRequest.id}`,
    );

    // Handle updates to the payment request if body is provided
    if (body) {
      // Validate invoice IDs if provided
      if (body.invoiceIds) {
        await this.invoiceReportService.validateInvoiceIds(
          body.invoiceIds,
          existingPaymentRequest.purchaseOrderId,
        );
      }

      // Get terms data if needed
      let termsData = existingPaymentRequest.termsData;
      if (body.terms || body.termsData) {
        const { termsData: newTermsData } = await this.getPurchaseOrderDetails({
          id: existingPaymentRequest.purchaseOrderId,
          employeeId: body?.termsData?.employeeId,
          userFromToken,
        });
        termsData = newTermsData;
      }

      // Calculate total amount if invoice IDs are provided
      let totalAmount = existingPaymentRequest.totalAmount;
      if (body.invoiceIds) {
        const invoiceReports = await this.invoiceReportRepository.findAll({
          where: { id: { [this.Sequelize.Op.in]: body.invoiceIds } },
          attributes: ['invoiceAmount'],
          raw: true,
        });

        totalAmount = invoiceReports.data.reduce(
          (sum, report) => sum + Number(report.invoiceAmount),
          0,
        );
        totalAmount = Number(totalAmount.toFixed(2));
      }

      // Update payment request with new data
      const updateData = {
        ...body,
        termsData,
        totalAmount,
        status: RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
      };

      // Remove fields that shouldn't be updated
      delete updateData.id;
      delete updateData.prLetter;
      delete updateData.prNumber;
      delete updateData.prDraftNumber;

      if (updateData.includesIncidentalFees) {
        updateData.includesIncidentalFees =
          updateData.includesIncidentalFees === 'true';
      }

      await this.rsPaymentRequestRepository.update(
        { id: existingPaymentRequest.id },
        updateData,
        { transaction },
      );

      // Update invoice associations if invoice IDs are provided
      if (body.invoiceIds) {
        await this.updateInvoiceAssociations(
          existingPaymentRequest.id,
          body.invoiceIds,
          transaction,
        );
      }
    } else {
      // If no updates, just change the status
      await this.rsPaymentRequestRepository.update(
        { id: existingPaymentRequest.id },
        { status: RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL },
        { transaction },
      );
    }

    // Reset approver statuses
    await this.rsPaymentRequestApproverRepository.update(
      {
        paymentRequestId: existingPaymentRequest.id,
        status: PR_APPROVER_STATUS.REJECTED,
      },
      { status: PR_APPROVER_STATUS.PENDING },
      { transaction },
    );

    // Send notification to approvers
    const approvers = await this.rsPaymentRequestApproverRepository.findAll({
      where: { paymentRequestId: existingPaymentRequest.id },
      attributes: ['userId'],
      paginate: false,
    });

    const approverIds = approvers.data
      .map((approver) => approver.userId)
      .filter(Boolean);

    if (approverIds.length > 0) {
      const prNumber = `VR-${existingPaymentRequest.prLetter}${existingPaymentRequest.prNumber}`;

      await this.notificationService.sendNotification({
        transaction,
        senderId: userFromToken.id,
        type: NOTIFICATION_TYPES.PAYMENT_REQUEST,
        title: NOTIFICATION_DETAILS.RESUBMIT_PAYMENT_REQUEST.title,
        message:
          NOTIFICATION_DETAILS.RESUBMIT_PAYMENT_REQUEST.message(prNumber),
        recipientUserIds: approverIds,
        metaData: {
          resubmittedBy: userFromToken.id,
          paymentRequestId: existingPaymentRequest.id,
          requisitionId: existingPaymentRequest.requisitionId,
        },
      });
    }

    this.fastify.log.info(
      `Successfully resubmitted payment request with ID: ${existingPaymentRequest.id}`,
    );
  }

  async validatePaymentRequestAmount(
    purchaseOrderId,
    totalAmount,
    paymentRequestId = null,
  ) {
    // Get purchase order details
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: { id: purchaseOrderId },
      attributes: ['id', 'totalDiscountedAmount'],
    });

    if (!purchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase Order with ID: ${purchaseOrderId} not found`,
      });
    }

    // Get existing payment requests for this PO (excluding current one if editing)
    const existingPaymentRequests =
      await this.rsPaymentRequestRepository.findAll({
        where: {
          purchaseOrderId,
          ...(paymentRequestId && {
            id: { [this.Sequelize.Op.ne]: paymentRequestId },
          }),
        },
        attributes: ['id', 'totalAmount'],
        paginate: false,
      });

    // Calculate total amount of existing payment requests
    const existingTotal = existingPaymentRequests.data.reduce(
      (sum, pr) => sum + parseFloat(pr.totalAmount || 0),
      0,
    );

    // Calculate new total with current payment request
    const newTotal = existingTotal + parseFloat(totalAmount);
    const poAmount = parseFloat(purchaseOrder.totalDiscountedAmount);
    const remainingAmount = poAmount - existingTotal;

    // Validate that total doesn't exceed PO amount
    if (newTotal > poAmount) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Payment request amount exceeds remaining PO amount`,
        description: {
          poAmount: poAmount.toFixed(2),
          existingPaymentTotal: existingTotal.toFixed(2),
          remainingAmount: remainingAmount.toFixed(2),
          requestedAmount: parseFloat(totalAmount).toFixed(2),
        },
      });
    }

    return {
      isValid: true,
      poAmount,
      existingTotal,
      remainingAmount,
      requestedAmount: parseFloat(totalAmount),
    };
  }

  async getIncidentalFeesStatus(purchaseOrderId) {
    // Get purchase order with incidental fees
    const purchaseOrder =
      await this.purchaseOrderRepository.getById(purchaseOrderId);

    if (!purchaseOrder) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase order with ID ${purchaseOrderId} not found`,
      });
    }

    // Check if PO has any incidental fees
    const hasIncidentalFees = !!(
      purchaseOrder.withholdingTaxDeduction ||
      purchaseOrder.deliveryFee ||
      purchaseOrder.tip ||
      purchaseOrder.extraCharges
    );

    // If no incidental fees, return early
    if (!hasIncidentalFees) {
      return {
        hasIncidentalFees: false,
        isIncidentalFeesUsed: false,
        data: null,
      };
    }

    // Check if any payment request already includes these fees
    const paymentRequests = await this.rsPaymentRequestRepository.findAll({
      where: {
        purchaseOrderId,
        includesIncidentalFees: true,
      },
      paginate: false,
    });

    const isIncidentalFeesUsed = paymentRequests.total > 0;

    // Format incidental fees for response
    const data = {
      withholdingTaxDeduction: Number(
        purchaseOrder.withholdingTaxDeduction || 0,
      ),
      deliveryFee: Number(purchaseOrder.deliveryFee || 0),
      tip: Number(purchaseOrder.tip || 0),
      extraCharges: Number(purchaseOrder.extraCharges || 0),
      total: Number(
        (
          Number(purchaseOrder.withholdingTaxDeduction || 0) +
          Number(purchaseOrder.deliveryFee || 0) +
          Number(purchaseOrder.tip || 0) +
          Number(purchaseOrder.extraCharges || 0)
        ).toFixed(2),
      ),
    };

    return {
      hasIncidentalFees,
      isIncidentalFeesUsed,
      data,
    };
  }

  async getAllItemsInPaymentRequestByPurchaseOrder(paymentRequestId) {
    const paymentRequest =
      await this.rsPaymentRequestRepository.getAllItemsInPaymentRequestByPurchaseOrder(
        paymentRequestId,
      );

    let supplier;
    if (paymentRequest.purchaseOrder.supplierType === 'supplier') {
      const result = await this.supplierRepository.findOne({
        attributes: ['id', 'name', 'tin'],
        where: {
          id: paymentRequest.purchaseOrder.supplierId,
        },
      });

      supplier = result;
    } else if (paymentRequest.purchaseOrder.supplierType === 'company') {
      const result = await this.companyRepository.findOne({
        attributes: ['id', 'name', 'tin'],
        where: {
          id: paymentRequest.purchaseOrder.supplierId,
        },
      });

      supplier = result;
    } else if (paymentRequest.purchaseOrder.supplierType === 'project') {
      const result = await this.projectRepository.findOne({
        attributes: ['id', 'name'],
        where: {
          id: paymentRequest.purchaseOrder.supplierId,
        },
      });

      supplier = result;
    }
    return { ...paymentRequest, supplier };
  }
}

module.exports = RsPaymentRequestService;
