const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
class SupplierService {
  constructor({
    supplierRepository,
    syncRepository,
    fastify,
    utils,
    db,
    clientErrors,
    canvassRequisitionRepository,
    requisitionRepository,
    notificationRepository,
    constants,
    requisitionBadgeRepository,
    userRepository,
    canvassApproverRepository,
    canvassItemSupplierRepository,
    purchaseOrderRepository,
    purchaseOrderApproverRepository,
    roleService,
    notificationService,
  }) {
    this.httpClient = new utils.HTTPClient({
      endpoint: `${process.env.CITYLAND_API_URL}`,
      logger: fastify.log,
    });
    this.supplierRepository = supplierRepository;
    this.syncRepository = syncRepository;
    this.fastify = fastify;
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.requisitionRepository = requisitionRepository;
    this.notificationRepository = notificationRepository;
    this.constants = constants;
    this.utils = utils;
    this.requisitionBadgeRepository = requisitionBadgeRepository;
    this.userRepository = userRepository;
    this.canvassApproverRepository = canvassApproverRepository;
    this.canvassItemSupplierRepository = canvassItemSupplierRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.purchaseOrderApproverRepository = purchaseOrderApproverRepository;
    this.roleService = roleService;
    this.notificationService = notificationService;
  }

  async syncSuppliers(payload) {
    const { id: userId } = payload.userFromToken;
    await this.sync({ userId });
    return true;
  }

  async sync({ userId }) {
    try {
      // Add query logging to catch the exact failing query
      this.db.sequelize.options.logging = (sql, timing) => {
        console.log('SEQUELIZE QUERY:', sql);
      };

      const authResult = await this.httpClient.post({
        path: '/token/',
        body: {
          username: process.env.CITYLAND_API_USERNAME,
          password: process.env.CITYLAND_API_PASSWORD,
        },
      });
      const data = await this.httpClient.get({
        path: '/suppliers',
        headers: {
          Authorization: `Bearer ${authResult.access}`,
        },
      });

      this.logToFile({
        message: 'START_PROCESS',
        data,
      });

      const batchSize = 100;

      try {
        for (let i = 0; i < data.length; i += batchSize) {
          this.logToFile({ message: `BATCH_${i / batchSize + 1}: started` });

          const batch = data.slice(i, i + batchSize);
          const mappedSuppliers = batch.map((supplier) => ({
            userId,
            name: `${supplier.SUPPLIER_FIRST_NAME?.trim()} ${supplier.SUPPLIER_MIDDLE_INITIAL?.trim()} ${supplier.SUPPLIER_LAST_NAME?.trim()} ${supplier.SUPPLIER_EXTENSION_NAME?.trim()}`,
            tin: supplier.TIN?.trim(),
            address: supplier.SUPPLIER_ADDRESS?.trim(),
            citizenshipCode: supplier.CITIZENSHIP?.trim(),
            natureOfIncome: supplier.NATURE_OF_INCOME?.trim(),
            payCode: supplier.SUPPLIER_CODE?.trim(),
            iccode: supplier.CORPORATE_CODE?.trim(),
            status: 'ACTIVE',
          }));

          this.logToFile({ mappedSuppliers });

          const suppliersWithUpdate =
            await this.supplierWithUpdate(mappedSuppliers);

          this.logToFile({
            message: 'SUPPLIERS_WITH_UPDATE_TEST',
            data: suppliersWithUpdate.length,
          });

          if (suppliersWithUpdate.length > 0) {
            this.logToFile({
              message: `SUPPLIERS_WITH_UPDATE_${i / batchSize + 1}`,
              data: suppliersWithUpdate.length,
            });

            await suppliersWithUpdate.forEach(async (supplier) => {
              await this.processNotification({
                supplier: supplier,
                userId,
                notificationType: 'SUPPLIER_SYNC',
              });
            });
          }
          await this.supplierRepository.bulkCreate(mappedSuppliers);
          this.logToFile({ message: `BATCH_${i / batchSize + 1}: completed` });
          await this.randomDelay();
        }
      } catch (error) {
        this.logToFile({
          message: 'ERROR_BATCH_PROCESS',
          data: error,
        });
      }

      const rolesToNotif = await this.roleService.getSyncRecipientsRoleId();
      const { NOTIFICATION_DETAILS, NOTIFICATION_TYPES } =
        this.constants.notification;

      await this.syncRepository.updateLastSynced('supplier');
      await Promise.all(
        rolesToNotif.map(async (roleId) => {
          await this.notificationService.sendNotification({
            title: NOTIFICATION_DETAILS.SUPPLIER_SYNC_DONE.title,
            message: NOTIFICATION_DETAILS.SUPPLIER_SYNC_DONE.message,
            type: NOTIFICATION_TYPES.SUPPLIER_SYNC,
            recipientRoleId: roleId,
            senderId: userId,
          });
        }),
      );

      this.logToFile({ message: 'END_PROCESS' });

      return true;
    } catch (error) {
      this.logToFile({
        message: 'ERROR_SYNCING_SUPPLIERS',
        data: JSON.stringify(error),
      });
    }
  }

  async processNotification({ supplier, userId, notificationType }) {
    const transaction = await this.db.sequelize.transaction();
    try {
      await this.notifyRSUsers({
        userId,
        transaction,
        notificationType,
        supplierId: supplier.id,
        supplierName: supplier.name,
        isSuspended: supplier.status === 'SUSPENDED',
      });

      await transaction.commit();
    } catch (error) {
      this.logToFile({
        message: 'ERROR_SYNCING_SUPPLIERS',
        data: error,
      });
      await transaction.rollback();
    }
  }

  async notifyRSUsers({
    userId,
    transaction,
    notificationType,
    supplierId,
    supplierName,
    isSuspended,
  }) {
    try {
      const rsDetails =
        await this.getRequisitionAndUserAssignmentsBySupplierId(supplierId);
      if (!rsDetails || !userId) {
        this.logToFile({
          message: 'INVALID_REQUISITION_IDS_OR_USER_ID',
          data: { requisitionIds, userId },
        });
        throw new Error('Invalid requisitionIds or userId');
      }

      const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
        this.constants.notification;

      this.logToFile({
        message: 'RS_DETAILS',
        data: rsDetails,
      });

      if (rsDetails.total === 0) {
        this.logToFile({
          message: 'NO_RS_DETAILS_FOUND',
        });
        return;
      }

      this.fastify.log.info(`Creating notifications...`);
      await Promise.all(
        rsDetails.map(async (item) => {
          const canvassStatus = ['for_cs_approval', 'for_cs_approval'];
          await this.notificationRepository.create(
            {
              title: NOTIFICATION_DETAILS[notificationType].title,
              message: NOTIFICATION_DETAILS[notificationType].message,
              type: this.getNotificationType(
                item.status,
                NOTIFICATION_TYPES,
                canvassStatus,
              ),
              recipientUserIds: [
                item.assigned_to || null,
                item.created_by || null,
                item.purchasing_head || null,
              ].filter(Boolean),
              senderId: userId,
              metaData: {
                requisitionId: item.id,
                canvassId: item.canvass_requisition_id ?? null,
              },
            },
            { transaction },
          );

          if (canvassStatus.includes(item.status)) {
            this.logToFile({
              message: 'UPDATE_CANVASS_RS',
            });

            await this.resetCanvassColumns({
              requisitionId: item.id,
              supplierId,
              isSuspended,
              transaction,
              supplierName,
            });
          }

          await this.updatePurchaseOrders({
            supplierId,
            supplierName,
            isSuspended,
            transaction,
          });
        }),
      );

      this.logToFile({
        message: 'NOTIFICATION_CREATED',
      });

      return true;
    } catch (error) {
      this.logToFile({
        message: 'ERROR_NOTIFYING_RS_USERS',
        data: error,
      });
      throw error;
    }
  }

  getNotificationType(status, NOTIFICATION_TYPES, canvassStatus) {
    if (canvassStatus.includes(status)) {
      return NOTIFICATION_TYPES.CANVASS;
    }

    return NOTIFICATION_TYPES.REQUISITION_SLIP;
  }

  async getNotifRecipients(rsDetails, transaction) {
    const { USER_TYPES } = this.constants.user;
    const { SUPPLIER_SYNC_ACCEPTED_STATUSES } = this.constants.sync;
    const recipients = [];
    const rsPurchasingStaffs = rsDetails
      .filter((item) => SUPPLIER_SYNC_ACCEPTED_STATUSES.includes(item.status))
      .map((item) => item.assignedTo);
    const requesterIds = rsDetails.map((item) => item.createdBy);
    const purchasingHeadId = await this.userRepository.findOne(
      {
        attributes: ['id'],
        include: [
          {
            model: this.db.roleModel,
            as: 'role',
            where: {
              name: USER_TYPES.PURCHASING_HEAD,
            },
          },
        ],
      },
      { transaction },
    );

    if (rsPurchasingStaffs.length > 0) {
      recipients.push(...rsPurchasingStaffs);
    }

    if (requesterIds.length > 0) {
      recipients.push(...requesterIds);
    }

    if (purchasingHeadId) {
      recipients.push(purchasingHeadId.id);
    }

    this.logToFile({
      message: 'RECIPIENTS',
      data: recipients,
    });

    return recipients;
  }

  async resetCanvassColumns({
    requisitionId,
    supplierId,
    supplierName,
    isSuspended,
    transaction,
  }) {
    try {
      const canvassRs = await this.canvassRequisitionRepository.update(
        { requisitionId },
        { status: 'partially_canvassed' },
        { transaction, returning: true },
      );

      this.logToFile({
        message: 'UPDATE_CANVASS_APPROVERS',
        data: canvassRs,
      });

      if (canvassRs.length > 0 && canvassRs[1].length > 0) {
        await this.canvassApproverRepository.update(
          { canvassRequisitionId: canvassRs[1].map((item) => item.id) },
          { status: 'pending' },
          { transaction },
        );

        this.logToFile({
          message: 'UPDATE_CANVASS_ITEM_SUPPLIER',
        });

        if (isSuspended) {
          await this.canvassItemSupplierRepository.update(
            { supplierId, supplierType: 'supplier', supplierNameLocked: false },
            {
              supplierId: null,
            },
            { transaction },
          );
        }
      }

      this.logToFile({
        message: 'END_RESET_CANVASS_COLUMNS',
      });

      return true;
    } catch (error) {
      this.logToFile({
        message: 'ERROR_UPDATING_CANVASS_COLUMNS',
        data: error,
      });
    }
  }

  async updatePurchaseOrders({
    supplierId,
    supplierName,
    isSuspended,
    transaction,
  }) {
    const requisitions = await this.requisitionRepository.findAll({
      attributes: ['id'],
      where: {
        status: 'for_po_review',
      },
    });

    if (requisitions.data.length > 0) {
      if (isSuspended) {
        const purchaseOrders = await this.purchaseOrderRepository.update(
          {
            requisitionId: requisitions.data.map((item) => item.id),
            supplierId,
          },
          {
            status: 'cancelled_po',
          },
          { transaction, returning: true },
        );

        if (purchaseOrders.length > 0 && purchaseOrders[1].length > 0) {
          const purchaseOrderIds = purchaseOrders[1].map((item) => item.id);

          await this.purchaseOrderApproverRepository.destroy(
            {
              purchaseOrderId: {
                [this.db.Sequelize.Op.in]: purchaseOrderIds,
              },
            },
            {
              transaction,
            },
          );
        }
      } else {
        await this.purchaseOrderRepository.update(
          {
            requisitionId: requisitions.data.map((item) => item.id),
            supplierId,
            status: 'po_approval',
          },
          {
            supplierName,
          },
          { transaction },
        );
      }
    }
  }

  async logToFile({ message, data = '' }) {
    const logFile = path.join(
      __dirname,
      `../../infra/logs/${path.basename(__filename)}.log`,
    );
    if (!fs.existsSync(logFile)) {
      fs.writeFileSync(logFile, '');
    }

    let logMessage = `${new Date().toISOString()}: ${message}`;

    if (data) {
      logMessage += ` ${JSON.stringify(data, null, 2)}`;
    }

    fs.appendFileSync(logFile, `${logMessage}\n`);
  }

  async supplierWithUpdate(mappedSuppliers) {
    const existingSuppliers = await this.supplierRepository.findAll({
      where: {
        payCode: mappedSuppliers.map((supplier) => supplier.payCode),
      },
      attributes: [
        'id',
        'name',
        'tin',
        'address',
        'citizenshipCode',
        'natureOfIncome',
        'payCode',
        'iccode',
      ],
      paginate: false,
    });

    this.logToFile({
      message: 'EXISTING_SUPPLIERS',
      data: existingSuppliers,
    });

    this.logToFile({
      message: 'MAPPED_SUPPLIERS',
      data: mappedSuppliers,
    });

    const suppliersWithUpdate = existingSuppliers.data.filter(
      (existingSupplier) => {
        const mappedSupplier = mappedSuppliers.find(
          (supplier) => supplier.payCode === existingSupplier.payCode,
        );
        if (mappedSupplier) {
          const isMatch =
            existingSupplier.name === mappedSupplier.name &&
            existingSupplier.tin === mappedSupplier.tin &&
            existingSupplier.address === mappedSupplier.address &&
            existingSupplier.citizenshipCode ===
              mappedSupplier.citizenshipCode &&
            existingSupplier.natureOfIncome === mappedSupplier.natureOfIncome &&
            existingSupplier.payCode === mappedSupplier.payCode &&
            existingSupplier.iccode === mappedSupplier.iccode;

          return !isMatch;
        }

        return true;
      },
    );

    this.logToFile({
      message: 'SUPPLIERS_WITH_UPDATE',
      data: suppliersWithUpdate,
    });

    return suppliersWithUpdate;
  }

  async getRequisitionAndUserAssignmentsBySupplierId(supplierId) {
    try {
      this.logToFile({
        message: 'GET_REQUISITION_AND_USER_ASSIGNMENTS_BY_SUPPLIER_ID',
        data: supplierId,
      });
      const purchasingHead = await this.userRepository.getPurchasingHead();
      this.logToFile({
        message: 'GET_REQUISITION_AND_USER_ASSIGNMENTS_BY_SUPPLIER_ID',
        data: purchasingHead.id,
      });
      const { REQUISITION_STATUS } = this.constants.requisition;
      this.logToFile({
        message: 'GET_REQUISITION_AND_USER_ASSIGNMENTS_BY_SUPPLIER_ID',
        data: REQUISITION_STATUS.RS_IN_PROGRESS,
      });

      // Use raw SQL to avoid TimescaleDB + Sequelize alias conflicts
      const requisitions = await this.db.sequelize.query(
        `
              SELECT
                "requisition"."id",
                "requisition"."assigned_to",
                "requisition"."created_by",
                :purchasingHeadId AS "purchasing_head",
                "requisition"."status",
                "canvassRequisitions"."id" AS "canvass_requisition_id"
              FROM
                "requisitions" AS "requisition"
              JOIN
                "canvass_requisitions" AS "canvassRequisitions" ON "requisition"."id" = "canvassRequisitions"."requisition_id"
              JOIN
                "canvass_items" AS "canvassItems" ON "canvassRequisitions"."id" = "canvassItems"."canvass_requisition_id"
              JOIN
                "canvass_item_suppliers" AS "suppliers" ON "canvassItems"."id" = "suppliers"."canvass_item_id"
              WHERE
                "requisition"."status" = :status
                AND "suppliers"."supplier_id" = :supplierId
                AND "suppliers"."supplier_type" = 'supplier'
      `,
        {
          replacements: {
            purchasingHeadId: purchasingHead.id,
            supplierId: supplierId,
            status: REQUISITION_STATUS.RS_IN_PROGRESS,
          },
          type: this.db.Sequelize.QueryTypes.SELECT,
        },
      );

      this.logToFile({
        message: 'GET_REQUISITION_AND_USER_ASSIGNMENTS_BY_SUPPLIER_ID',
        data: 'TEST 3',
      });

      return requisitions;
    } catch (error) {
      this.logToFile({
        message: 'ERROR_GET_REQUISITION_AND_USER_ASSIGNMENTS_BY_SUPPLIER_ID',
        data: JSON.stringify(error),
      });
      throw error;
    }
  }

  async randomDelay(time = 100) {
    const delay = Math.floor(Math.random() * time) + 100;
    return new Promise((resolve) => setTimeout(resolve, delay));
  }

  async reDoCSApproval(payload) {
    const { supplier } = payload;
    await this.canvassItemSupplierRepository.getCanvassIdsBySupplier(supplier);
  }
}

module.exports = SupplierService;
