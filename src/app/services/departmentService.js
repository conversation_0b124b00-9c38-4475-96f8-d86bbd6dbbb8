class DepartmentService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      clientErrors,
      departmentRepository,
      userRepository,
      companyRepository,
      syncRepository,
      fastify,
    } = container;

    this.db = db;
    this.utils = utils;
    this.clientErrors = clientErrors;
    this.departmentEntity = entities.department;
    this.departmentRepository = departmentRepository;
    this.userRepository = userRepository;
    this.companyRepository = companyRepository;
    this.syncRepository = syncRepository;
    this.httpClient = new utils.HTTPClient({
      endpoint: `${process.env.CITYLAND_API_URL}`,
      logger: fastify.log,
    });
  }

  async syncDepartments(userDetails) {
    const authResult = await this.httpClient.post({
      path: '/token/',
      body: {
        username: process.env.CITYLAND_API_USERNAME,
        password: process.env.CITYLAND_API_PASSWORD,
      },
    });
    const departments = await this.httpClient.get({
      path: '/departments',
      headers: {
        Authorization: `Bearer ${authResult.access}`,
      },
    });
    const mappedDepartments = departments.map((department) => ({
      code: department.DEPARTMENT_CODE.trim(),
      name: department.DEPARTMENT_NAME.trim(),
    }));

    await this.departmentRepository.syncDepartments(
      mappedDepartments,
      userDetails,
    );

    return await this.syncRepository.updateLastSynced('department');
  }

  async getDepartmentById(id) {
    const department = await this.departmentRepository.getDepartmentDetails(id);

    if (!department) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Department not found',
      });
    }

    return {
      ...department,
      isAssociation: department.code == process.env.ASSOCIATION_DEPARTMENT_CODE,
    };
  }
}

module.exports = DepartmentService;
