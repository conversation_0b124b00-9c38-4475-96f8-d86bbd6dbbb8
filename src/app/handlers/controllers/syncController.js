class Async {
  constructor (container) {
    const {
      syncService,
      fastify,
    } = container;

    this.syncService = syncService;
    this.fastify = fastify;
  }

  async getSyncStatus(request, reply) {
    try {
      const { jobId } = request.params;
      const job = await this.syncService.getJob(jobId);

      if (!job) {
        return reply.code(200).send({
          success: false,
          message: 'Job not found',
          state: 'job_not_found'
        });
      }

      const state = await job.getState();
      const progress = job.progress || 0;

      return reply.code(200).send({
        success: true,
        jobId: job.id,
        state,
        progress,
        data: job.data,
        returnvalue: job.returnvalue,
        failedReason: job.failedReason,
        stacktrace: job.stacktrace,
        timestamp: job.timestamp,
        processedOn: job.processedOn,
        finishedOn: job.finishedOn
      });
    } catch (error) {
      this.fastify.log.error(`ERROR_GET_SYNC_STATUS: ${error.message}`);

      return reply.code(500).send({
        success: false,
        message: 'Failed to get sync status'
      });
    }
  }

  async destroyByJobId(request, reply) {
    try {
      const { jobId } = request.params;
      const job = await this.syncService.getJob(jobId);

      if (!job) {
        return reply.code(200).send({
          success: false,
          message: 'Job not found',
          state: 'job_not_found'
        });
      }

      await job.remove();

      return reply.code(200).send({
        message: `Job ID ${jobId} successfully deleted`
      });
    } catch (error) {
      this.fastify.log.error(`ERROR_DESTROY_JOB_ID: ${error.message}`);
      return reply.code(500).send({
        success: false,
        message: 'Failed to get sync status'
      });
    }
  }
}

module.exports = Async;
