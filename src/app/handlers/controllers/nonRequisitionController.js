const fs = require('fs');
const stream = require('stream');

class NonRequisition {
  constructor(container) {
    const {
      db,
      utils,
      fastify,
      entities,
      constants,
      noteService,
      userService,
      clientErrors,
      chargeToService,
      attachmentService,
      notificationService,
      nonRequisitionService,
      templateService,
      noteRepository,
      companyRepository,
      projectRepository,
      nonRequisitionRepository,
      nonRequisitionItemRepository,
      nonRequisitionHistoryRepository,
      nonRequisitionApproverRepository,
      companyService,
    } = container;

    this.db = db;
    this.utils = utils;
    this.constants = constants;
    this.userService = userService;
    this.noteService = noteService;
    this.fastify = fastify;
    this.noteRepository = noteRepository;
    this.clientErrors = clientErrors;
    this.chargeToService = chargeToService;
    this.attachmentService = attachmentService;
    this.notificationService = notificationService;
    this.nonRequisitionService = nonRequisitionService;
    this.templateService = templateService;
    this.companyRepository = companyRepository;
    this.projectRepository = projectRepository;
    this.nonRequisitionEntity = entities.nonRequisition;
    this.nonRequisitionRepository = nonRequisitionRepository;
    this.nonRequisitionItemRepository = nonRequisitionItemRepository;
    this.nonRequisitionHistoryRepository = nonRequisitionHistoryRepository;
    this.nonRequisitionApproverRepository = nonRequisitionApproverRepository;
    this.companyService = companyService;
  }

  async getAllNonRSHistoryById(request, reply) {
    const { id } = request.params;
    const { sortBy, filterBy, ...queries } = request.query;
    const { nonRSHistorySortSchema, nonRSHistoryFilterSchema } =
      this.nonRequisitionEntity;

    const parsedSortBy = nonRSHistorySortSchema.parse(sortBy);
    const parsedFilterBy = nonRSHistoryFilterSchema.parse(filterBy);

    const filterByWhereClause =
      this.utils.buildFilterWhereClause(parsedFilterBy);

    try {
      const nonRSHistory =
        await this.nonRequisitionHistoryRepository.getAllHistories({
          where: { nonRequisitionId: id },
          filterBy: filterByWhereClause,
          order: parsedSortBy,
          ...queries,
        });

      return reply.status(200).send(nonRSHistory);
    } catch (error) {
      throw error;
    }
  }

  async getAllNonRs(request, reply) {
    const { sortBy, filterBy, ...queries } = request.query;
    const { nonRSSortSchema, nonRSFilterSchema } = this.nonRequisitionEntity;

    const parsedSortBy = nonRSSortSchema.parse(sortBy);
    const parsedFilterBy = nonRSFilterSchema.parse(filterBy);

    const orderClauses = parsedSortBy?.map(([field, direction]) => {
      if (field === 'requestor') {
        return [
          this.db.Sequelize.literal('"requestor"."first_name"'),
          direction,
        ];
      }

      return [field, direction];
    });

    const filterByWhereClause =
      this.utils.buildFilterWhereClause(parsedFilterBy);

    const nonRsList = await this.nonRequisitionRepository.getAllNonRs({
      filterBy: filterByWhereClause,
      order: orderClauses,
      userId: request.userFromToken.id,
      ...queries,
    });

    return reply.status(200).send(nonRsList);
  }

  async getNonRSDetails(request, reply) {
    const { id } = request.params;

    try {
      const nonRsDetails = await this.nonRequisitionService.getExistingNonRs(
        parseInt(id),
      );

      return reply.status(200).send(nonRsDetails);
    } catch (error) {
      throw error;
    }
  }

  async getNonRSItemList(request, reply) {
    const { id } = request.params;

    const nonRsId = parseInt(id);
    try {
      await this.nonRequisitionService.getExistingNonRs(nonRsId);

      const itemList =
        await this.nonRequisitionItemRepository.getAllItemListByNonRsId(
          nonRsId,
          {
            paginate: false,
          },
        );

      return reply.status(200).send(itemList);
    } catch (error) {
      throw error;
    }
  }

  async getNonRSApprovers(request, reply) {
    const { id } = request.params;

    const nonRsId = parseInt(id);
    try {
      await this.nonRequisitionService.getExistingNonRs(nonRsId);

      const approvers =
        await this.nonRequisitionApproverRepository.getApprovers(nonRsId);

      return reply.status(200).send(approvers);
    } catch (error) {
      throw error;
    }
  }

  async createNonRS(request, reply) {
    const { userFromToken, transaction } = request;
    const { MODELS } = this.constants.attachment;
    const { USER_TYPES, COMMENT_TYPES } = this.constants.note;
    const {
      notes,
      invoiceNotes,
      attachmentIds = [],
      invoiceAttachmentIds = [],
      ...restBody
    } = request.body;
    let project;

    if (restBody.chargeTo && restBody.chargeToId) {
      const isChargeToValid = await this.chargeToService.isValidChargeTo({
        chargeTo: restBody.chargeTo,
        chargeToId: restBody.chargeToId,
      });

      if (!isChargeToValid) {
        throw this.clientErrors.NOT_FOUND({
          message: `Charge to (${restBody.chargeTo}) details not found`,
        });
      }
    }

    const company = await this.companyRepository.getById(restBody.companyId);

    if (restBody.category === 'project' && restBody.projectId === undefined) {
      throw this.clientErrors.NOT_FOUND({
        message: `Project Cannot be Empty`,
      });
    }

    if (!company) {
      throw this.clientErrors.NOT_FOUND({
        message: `Company with ID ${restBody.companyId} not found`,
      });
    }

    if (restBody.projectId) {
      project = await this.projectRepository.getById(restBody?.projectId);

      if (!project) {
        throw this.clientErrors.NOT_FOUND({
          message: `Project with ID ${restBody.projectId} not found`,
        });
      }

      const isProjectAssociated =
        await this.companyService.checkIfCompanyAssociatedToProject(
          restBody.category,
          restBody.companyId,
          restBody.projectId,
        );

      if (!isProjectAssociated) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Company and project are not associated with each other.`,
        });
      }
    }

    const nonRS = await this.nonRequisitionService.createNonRS({
      ...restBody,
      invoiceAttachmentIds,
      transaction,
      createdBy: userFromToken.id,
    });

    if (attachmentIds.length) {
      await this.attachmentService.assignAttachmentsToModelId(
        attachmentIds,
        MODELS.NON_RS,
        nonRS.id,
        userFromToken.id,
        transaction,
      );
    }

    if (invoiceAttachmentIds.length) {
      await this.attachmentService.assignAttachmentsToModelId(
        invoiceAttachmentIds,
        MODELS.NON_RS_INVOICE,
        nonRS.id,
        userFromToken.id,
        transaction,
      );
    }

    if (restBody.id && invoiceNotes) {
      await this.noteRepository.update(
        {
          model: 'non_requisition_invoice',
          modelId: restBody.id,
        },
        {
          note: invoiceNotes,
        },
      );
    }

    if (restBody.id && notes) {
      await this.noteRepository.update(
        {
          model: 'non_requisition',
          modelId: restBody.id,
        },
        {
          note: notes,
        },
      );
    }

    const existingNote = await this.noteRepository.findOne({
      where: {
        [this.db.Sequelize.Op.and]: [
          { model: 'non_requisition' },
          { modelId: nonRS.id },
        ],
      },
    });

    const existingInvoiceNote = await this.noteRepository.findOne({
      where: {
        [this.db.Sequelize.Op.and]: [
          { model: 'non_requisition_invoice' },
          { modelId: nonRS.id },
        ],
      },
    });

    if (notes && !existingNote) {
      await this.noteService.createNote(
        {
          model: MODELS.NON_RS,
          modelId: nonRS.id,
          userName: userFromToken.fullNameUser,
          userType: USER_TYPES.REQUESTOR,
          commentType: COMMENT_TYPES.NOTE,
          note: notes,
        },
        {
          transaction,
        },
      );
    }

    if (invoiceNotes && !existingInvoiceNote) {
      await this.noteService.createNote(
        {
          model: MODELS.NON_RS_INVOICE,
          modelId: nonRS.id,
          userName: userFromToken.fullNameUser,
          userType: USER_TYPES.REQUESTOR,
          commentType: COMMENT_TYPES.NOTE,
          note: invoiceNotes,
        },
        {
          transaction,
        },
      );
    }

    return reply.status(200).send({
      id: nonRS.id,
      message: 'Non RS entry successfully created',
    });
  }

  async cancelNonRS(request, reply) {
    const { userFromToken, transaction } = request;
    const { id } = request.params;
    const nonRsId = parseInt(id);

    const existingData =
      await this.nonRequisitionService.getExistingNonRs(nonRsId);

    const isCreator = existingData.createdBy === userFromToken.id;
    if (!isCreator) {
      throw this.clientErrors.FORBIDDEN({
        message: `You are not authorized to cancel this non-requisition`,
      });
    }

    await this.nonRequisitionRepository.cancelNonRS(nonRsId, {
      transaction,
    });

    return reply.status(200).send({
      message: 'Non RS entry successfully cancelled',
    });
  }

  async approveNonRs(request, reply) {
    const { userFromToken, transaction } = request;
    const { id: nonRsId } = request.params;
    const { approverId, itemList } = request.body;
    const { MODELS } = this.constants.attachment;
    const { USER_TYPES, COMMENT_TYPES } = this.constants.note;
    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;

    const existingNonRs = await this.nonRequisitionService.getExistingNonRs(
      parseInt(nonRsId),
    );

    if (request.body.approveReason) {
      await this.noteService.createNote(
        {
          model: MODELS.NON_RS,
          modelId: existingNonRs.id,
          userName: userFromToken.fullNameUser,
          userType: USER_TYPES.APPROVER,
          commentType: COMMENT_TYPES.APPROVAL,
          note: request.body.approveReason,
        },
        {
          transaction,
        },
      );
    }

    await this.nonRequisitionService.approveRejectNonRS({
      userFromToken,
      transaction,
      existingNonRs,
      approverId: userFromToken.id,
      hasApproved: true,
      toAddApproverId: approverId,
      itemList,
    });

    await this.notificationService.sendNotification({
      transaction,
      senderId: userFromToken.id,
      type: NOTIFICATION_TYPES.NON_RS,
      title: NOTIFICATION_DETAILS.APPROVE_NON_RS.title,
      message: NOTIFICATION_DETAILS.APPROVE_NON_RS.message,
      recipientUserIds: [existingNonRs.createdBy],
      metaData: {
        approvedBy: userFromToken.id,
        nonRsId: existingNonRs.id,
      },
    });

    return reply.status(200).send({
      message: 'Non-requisition approved successfully',
    });
  }

  async rejectNonRs(request, reply) {
    const { userFromToken, transaction } = request;
    const { rejectReason } = request.body;
    const { id: nonRsId } = request.params;
    const { MODELS } = this.constants.attachment;
    const { USER_TYPES, COMMENT_TYPES } = this.constants.note;
    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;

    const existingNonRs = await this.nonRequisitionService.getExistingNonRs(
      parseInt(nonRsId),
    );

    await this.nonRequisitionService.approveRejectNonRS({
      transaction,
      existingNonRs,
      approverId: userFromToken.id,
      hasApproved: false,
    });

    await this.noteService.createNote(
      {
        model: MODELS.NON_RS,
        modelId: existingNonRs.id,
        userName: userFromToken.fullNameUser,
        userType: USER_TYPES.APPROVER,
        commentType: COMMENT_TYPES.DISAPPROVAL,
        note: rejectReason,
      },
      {
        transaction,
      },
    );

    await this.notificationService.sendNotification({
      transaction,
      senderId: userFromToken.id,
      type: NOTIFICATION_TYPES.NON_RS,
      title: NOTIFICATION_DETAILS.REJECT_NON_RS.title,
      message: NOTIFICATION_DETAILS.REJECT_NON_RS.message,
      recipientUserIds: [existingNonRs.createdBy],
      metaData: {
        rejectedBy: userFromToken.id,
        rejectReason: rejectReason,
        nonRsId: existingNonRs.id,
      },
    });

    return reply.status(200).send({
      message: 'Non-requisition rejected successfully',
    });
  }

  async removeAdhocApprover(request, reply) {
    const { id } = request.params;
    const { userFromToken } = request;

    const nonRsId = parseInt(id);
    try {
      await this.nonRequisitionService.getExistingNonRs(nonRsId);

      await this.nonRequisitionService.removeAdhocApprover({
        id: nonRsId,
        primaryApproverId: userFromToken.id,
      });

      return reply.status(200).send({
        message: 'Non requisition approver successfully removed',
      });
    } catch (error) {
      throw error;
    }
  }

  async addAdhocApprover(request, reply) {
    const { userFromToken, transaction } = request;
    const { approverId } = request.body;
    const { id: nonRsId } = request.params;
    const { USER_TYPES } = this.constants.user;
    const { NOTIFICATION_TYPES } = this.constants.notification;

    const existingNonRs = await this.nonRequisitionService.getExistingNonRs(
      parseInt(nonRsId),
    );

    /* Ensure that user exist with valid role at first index */
    const approverRoleList = [
      ...Object.values(this.constants.user.APPROVERS),
      USER_TYPES.PURCHASING_STAFF,
    ];
    const users = await this.userService.validateMultipleUsers([approverId], {
      roleNames: approverRoleList,
    });

    await this.nonRequisitionService.addAdhocApprover({
      transaction,
      approver: users[0],
      creatorId: userFromToken.id,
      nonRsId: existingNonRs.id,
    });

    await this.notificationService.sendNotification({
      transaction,
      senderId: userFromToken.id,
      type: NOTIFICATION_TYPES.NON_RS,
      title: 'Assigned as an Additional Approver',
      message: `${userFromToken.firstName} ${userFromToken.lastName} has added you to review the Non-RS and have it Approved. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.`,
      recipientUserIds: [approverId],
      metaData: {
        addedBy: userFromToken.id,
        adhocApprover: approverId,
        nonRsId: existingNonRs.id,
        requisitionId: existingNonRs.requisitionId,
      },
    });

    return reply.status(200).send({
      message: 'Non requisition approver updated successfully',
    });
  }

  async getNonRSItemUnits(request, reply) {
    this.fastify.log.info(`Getting units of non rs items...`);
    const units = await this.nonRequisitionItemRepository.getNonRSItemUnits();
    return reply.status(200).send({ message: `Get Success`, data: units });
  }

  async generatePdf(request, reply) {
    const { id } = request.params;
    const nonRsId = parseInt(id);

    try {
      const [nonRs, items] = await Promise.all([
        this.nonRequisitionRepository.getById(nonRsId, {
          include: [
            {
              association: 'company',
              as: 'company',
              attributes: ['id', 'tin', 'name'],
            },
            {
              association: 'project',
              as: 'project',
              attributes: ['id', 'name'],
            },
          ],
        }),
        this.nonRequisitionItemRepository.getAllItemListByNonRsId(nonRsId, {
          paginate: false,
        }),
      ]);

      if (!items || !items.total) {
        throw this.clientErrors.BAD_REQUEST({
          message: 'No items found for this Non-RS',
        });
      }

      const headers = {
        projectCompany: nonRs.project?.name || nonRs.company?.name || '',
        rsNumber: `NRS-${nonRs.nonRsLetter}${nonRs.nonRsNumber}`,
        datePrepared: new Date(),
        dateNeeded: nonRs.invoiceDate,
        payableTo: nonRs.payableTo || '',
        tin: nonRs.company?.tin || '',
        requestBalance: nonRs.totalDiscountedAmount,
      };

      const allItems = items.data.map((item, index) => ({
        itemNum: index + 1,
        itemName: item.name,
        qty: item.quantity,
        unit: item.unit,
        unitPrice: item.amount,
        discountValue: item.amount - item.discountedPrice,
        discountedPrice: item.quantity * item.discountedPrice,
      }));

      const pageSize = 15;
      const pages = this.utils
        .paginateItems(allItems, pageSize)
        .map((items, index) => ({
          currentPage: index + 1,
          items,
          itemsCount: index * pageSize + 1,
          currentTotalItemsCount: Math.min(
            (index + 1) * pageSize,
            allItems.length,
          ),
        }));

      const data = {
        ...headers,
        totalPrice: nonRs.totalDiscountedAmount,
        pages,
        totalPages: pages.length,
        totalItems: allItems.length,
      };

      const result = await this.templateService.generateDynamicTemplate(
        data,
        'non-rs.hbs',
        'non_requisition_downloads',
        'NRS',
      );

      const fileStream = fs.createReadStream(result.filePath);

      return reply
        .header('Content-Type', 'application/pdf')
        .header(
          'Content-Disposition',
          `attachment; filename="${result.fileName}"`,
        )
        .send(fileStream);
    } catch (error) {
      throw error;
    }
  }
}
module.exports = NonRequisition;
