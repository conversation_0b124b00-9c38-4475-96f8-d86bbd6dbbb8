class Company {
  constructor(container) {
    const {
      db,
      entities,
      constants,
      clientErrors,
      userService,
      companyService,
      syncRepository,
      departmentService,
      companyRepository,
      requisitionService,
      utils,
      companySyncService,
      projectService,
      projectRepository,
      projectCompanyRepository,
    } = container;

    this.db = db;
    this.constants = constants;
    this.userService = userService;
    this.clientErrors = clientErrors;
    this.companyEntity = entities.company;
    this.companyService = companyService;
    this.projectService = projectService;
    this.syncRepository = syncRepository;
    this.departmentService = departmentService;
    this.companyRepository = companyRepository;
    this.requisitionService = requisitionService;
    this.utils = utils;
    this.companySyncService = companySyncService;
    this.projectRepository = projectRepository;
    this.projectCompanyRepository = projectCompanyRepository;
  }

  async createCompany(request, reply) {
    const createdCompany = await this.companyService.createCompany(
      request.body,
    );

    return reply.status(201).send(createdCompany);
  }

  async updateCompany(request, reply) {
    const previousValue = await this.companyRepository.getByCompanyId(
      request.params.companyId,
    );
    await this.companyService.updateCompany(
      request.params.companyId,
      request.body,
    );

    return reply.status(200).send({
      message: 'Company updated successfully',
      previousValue,
    });
  }

  async getCompany(request, reply) {
    const { companyId } = request.params;
    const existingCompany =
      await this.companyService.getExistingCompany(companyId);

    return reply.status(200).send(existingCompany);
  }

  async getCompanyTaggedProjects(request, reply) {
    const { companyId } = request.params;
    const {
      paginate = true,
      limit = 10,
      page = 1,
      sortBy = '{"createdAt": "desc"}',
      search,
    } = request.query;

    const order = Object.entries(JSON.parse(sortBy)).map(([key, value]) => {
      if (key === 'companyName') {
        const direction = value.toUpperCase()
        const sortNull = direction === 'ASC' ? 'NULLS FIRST' : 'NULLS LAST'
        return [
          { model: this.db.companyModel, as: 'company' },
          'name',
          `${direction} ${sortNull}`,
        ]
      } else {
        return [key, value.toUpperCase()];
      }
    });

    const companyTaggedProjects = await this.projectRepository.findAll({
      limit,
      page,
      order: [
        ...order,
        ['id', 'DESC'], /// deterministic tie-breaker
      ],
      include: [
        {
          model: this.db.companyModel,
          attributes: ['id', 'name'],
          as: 'company',
        },
        {
          model: this.db.companyModel,
          as: 'taggedCompanies',
          where: { id: companyId },
          through: { attributes: [] }, // Don't include junction table fields
          require: true,
        },
      ],
      ...(search
        ? {
            where: {
              name: { [this.db.Sequelize.Op.iLike]: `%${search}%` },
            },
          }
        : {}),
      paginate,
      subQuery: false,
      distinct: true
    });

    return reply.send(companyTaggedProjects);
  }

  async getCompanyUntaggedProjects(request, reply) {
    const untaggedProjects = await this.projectRepository.findAll({
      where: {
        //companyId: null,
        id: {
          [this.db.Sequelize.Op.ne]: null,
        },
      },
      order: [['name', 'asc']],
      paginate: false,
      include: [
        {
          model: this.db.companyModel,
          attributes: ['id', 'name'],
          as: 'company',
        },
        {
          model: this.db.companyModel,
          attributes: ['id', 'name'],
          as: 'taggedCompanies',
        },
      ],
    });

    return reply.status(200).send(untaggedProjects);
  }

  async getCompanyUntaggedProjectsForAnExsitingCompany(request, reply) {
    const untaggedProjects = await this.projectRepository.findAll({
      where: {
        [this.db.Sequelize.Op.or]: [
          { companyId: null },
          {
            companyId: parseInt(request.params.companyId),
          },
        ],
      },
      order: [['name', 'asc']],
      paginate: false,
    });

    return reply.status(200).send(untaggedProjects);
  }

  async removeCompanyProjectTag(request, reply) {
    const { companyId, projectId } = request.params;

    // Check if the association exists
    const existingTagging =
      await this.projectCompanyRepository.findByProjectAndCompany(
        projectId,
        companyId,
      );

    if (!existingTagging) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Project is not tagged to this company.',
      });
    }

    // Delete the association
    await this.projectCompanyRepository.removeTagging(projectId, companyId);

    return reply.status(200).send({
      message: `Project was successfully untagged from the company.`,
    });
  }

  async syncCompanies(request, reply) {
    const lastSyncedAt = await this.companySyncService.syncCompanies({
      userFromToken: request.userFromToken,
    });

    return reply.status(200).send({
      lastSyncedAt,
    });
  }

  async getAllCompanies(request, reply) {
    const { sortBy, filterBy, ...queries } = request.query;
    const { companySortSchema, companyFilterSchema } = this.companyEntity;

    const parsedSortBy = companySortSchema.parse(sortBy);

    const orderClauses = parsedSortBy?.map(([field, direction]) => {
      if (field === 'category') {
        return [this.db.Sequelize.literal('LOWER("companies"."category"::text)'), direction]
      }

      return [field, direction]
    })

    const parsedFilterBy = companyFilterSchema.parse(filterBy);

    const filterByWhereClause =
      this.utils.buildFilterWhereClause(parsedFilterBy);

    const companyList = await this.companyRepository.getAllCompanies({
      ...queries,
      filterBy: filterByWhereClause,
      order: orderClauses,
    });

    const companySync = await this.syncRepository.findByModel('company');

    return reply.status(200).send({
      ...companyList,
      lastSyncedAt: companySync?.lastSyncedAt || null,
    });
  }

  async deleteCompany(request, reply) {
    const existingCompany = await this.companyService.getExistingCompany(
      request.params.companyId,
    );

    if (existingCompany?.category !== 'association') {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Non-association companies cannot be deleted',
      });
    }

    const companyId = existingCompany.id;
    const hasRequisitions =
      await this.requisitionService.hasCompanyRequisitions(companyId);

    if (hasRequisitions) {
      throw this.clientErrors.FORBIDDEN({
        message: 'Cannot delete company with existing requisitions',
      });
    }

    await this.companyService.deleteCompany(companyId);

    // remove all tags related to this company
    await this.projectCompanyRepository.removeAllCompanyTags(companyId);

    return reply.status(200).send({
      message: 'Company association deleted successfully',
    });
  }
}

module.exports = Company;
