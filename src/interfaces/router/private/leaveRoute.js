async function leaveRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const leaveController = fastify.diScope.resolve('leaveController');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;

  //added if necessary
  fastify.route({
    method: 'GET',
    url: '/all',
    handler: leaveController.getAllLeaves.bind(leaveController),
  });

  fastify.route({
    method: 'GET',
    url: '/',
    handler: leaveController.getAllLeavesByUserId.bind(leaveController),
  });

  fastify.route({
    method: 'POST',
    url: '/',
    schema: {
      body: entities.leave.createLeaveSchema,
    },
    handler: leaveController.createLeave.bind(leaveController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id',
    schema: {
      params: entities.leave.getLeaveByIdSchema,
    },
    handler: leaveController.getLeaveByPk.bind(leaveController),
  });

  fastify.route({
    method: 'GET',
    url: '/:userId/user',
    schema: {
      params: entities.leave.getUserIdSchema,
    },
    handler: leaveController.getWorkflowsByUserId.bind(leaveController),
  });

  fastify.route({
    method: 'PUT',
    url: '/:leaveId/alt-approver',
    schema: {
      body: entities.leave.addAltApproverSchema,
    },
    handler: leaveController.addAltApprover.bind(leaveController),
  });

  fastify.route({
    method: 'PUT',
    url: '/alt-approver',
    schema: {
      body: entities.leave.addAltApproverSchema,
    },
    handler: leaveController.addAltApprover.bind(leaveController),
  });

   fastify.route({
    method: 'PUT',
    url: '/assign-alt-approver',
    schema: {
      body: entities.leave.assignApprover,
    },
    handler: leaveController.assignApprover.bind(leaveController),
  });

  fastify.route({
    method: 'PUT',
    url: '/:id',
    schema: {
      params: entities.leave.getLeaveByIdSchema,
      body: entities.leave.updateLeaveSchema,
    },
    handler: leaveController.updateLeave.bind(leaveController),
  });

  fastify.route({
    method: 'DELETE',
    url: '/:id',
    schema: {
      params: entities.leave.getLeaveByIdSchema,
    },
    handler: leaveController.deleteLeave.bind(leaveController),
  });
}

module.exports = leaveRoutes;
