const BaseRepository = require('./baseRepository');

class ProjectRepository extends BaseRepository {
  constructor ({ db }) {
    super(db.projectModel);

    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  async getByProjectId(projectId) {
    return await this.getById(projectId);
  }

  async getAllProjects(payload) {
    let whereClause = {};
    const { search, limit, page, paginate, filterBy, companyCode } = payload;

    const { name } = filterBy;

    if (name) {
      whereClause[this.Sequelize.Op.or] = [
        { name: { [this.Sequelize.Op.iLike]: `%${name}%` } },
      ];
    }

    if (search) {
      whereClause.name = { [this.Sequelize.Op.iLike]: `%${search}%` };
    }

    if (companyCode) {
      whereClause.companyCode = companyCode;
    }

    const formattedOrder = [];
    const orderObject = Object.entries(payload.order ?? { name: 'ASC' });
    for (const [field, direction] of orderObject) {
      switch (field) {
        case 'name':
          formattedOrder.push(['name', direction.toUpperCase()]);
          continue;
        case 'code':
          formattedOrder.push(['code', direction.toUpperCase()]);
          continue;
        case 'initial':
          formattedOrder.push([
            'initial', 
            direction.toUpperCase() === 'ASC' ? 'ASC NULLS FIRST' : 'DESC NULLS LAST'
          ]);
          continue;
        case 'company':
          const dir = direction.toUpperCase()
          const sortNull = dir === 'ASC' ? 'NULLS FIRST' : 'NULLS LAST'
          formattedOrder.push([
            this.Sequelize.literal('"company.name"'),
            `${dir} ${sortNull}`,
          ])
          continue;
        case 'address':
          formattedOrder.push(['address', direction.toUpperCase()]);
          continue;
        default:
          continue;
      }
    }

    return await this.findAll({
      limit,
      page,
      order: formattedOrder,
      paginate,
      where: whereClause,
      attributes: {
        exclude: ['companyCode'],
      },
      include: [
        {
          association: 'company',
          as: 'company',
          attributes: ['id', 'code', 'name', 'address'],
        },
      ],
    });
  }

  async syncProjects(payload) {
    return await this.tableName.bulkCreate(payload, {
      updateOnDuplicate: ['title', 'initial', 'companyCode', 'address'],
    });
  }

  async findProjectByIds(projectIds, options = {}) {
    return this.findAll({
      where: {
        id: {
          [this.Sequelize.Op.in]: projectIds,
        },
      },
      ...options,
    });
  }

  async getAllProjectTaggedCompanies() {
    return await this.findAll({
      paginate: false,
      include: [
        {
          model: this.db.companyModel,
          as: 'taggedCompanies',
          through: { attributes: [] },
          required: true,
        },
      ],
    });
  }
}

module.exports = ProjectRepository;
