const BaseRepository = require('./baseRepository');

class CanvassRequisitionRepository extends BaseRepository {
  constructor({ db, entities, utils }) {
    super(db.canvassRequisitionModel);

    this.db = db;
    this.Sequelize = db.Sequelize;
    this.utils = utils;
    this.entities = entities;
  }

  async getExistingCanvass(payload = {}, options = {}) {
    const { canvassId, requisitionId } = payload;

    if (!canvassId) return;

    return this.findOne({
      where: {
        id: canvassId,
        requisitionId,
      },
      ...options,
    });
  }

  async getAllCanvass(payload = {}) {
    const { requisitionId, limit, page, sortBy, paginate, filterBy } = payload;

    const whereClause = { requisitionId };
    const defaultOrder = [['createdAt', 'DESC']];

    const { canvassSortSchema, canvassFilterSchema } = this.entities.canvass;

    const parsedSortBy = canvassSortSchema.parse(sortBy);
    const orderClauses = parsedSortBy?.map(([field, direction]) => {
      if (field === 'canvassNumber') {
        return ['cs_number', direction];
      }

      if (field === 'lastApprover') {
        // technically next approver column.
        return [
          this.db.Sequelize.literal(`
          (
            SELECT (u.first_name || ' ' || u.last_name)
            FROM canvass_approvers ca
            JOIN users u ON u.id = ca.user_id
            WHERE ca.canvass_requisition_id = canvass_requisitions.id
              AND ca.status = 'pending'
            ORDER BY ca.level ASC,
                    CASE WHEN ca.is_adhoc = TRUE THEN 2 ELSE 1 END ASC,
                    u.first_name ASC,
                    u.last_name ASC
            LIMIT 1
          )
        `),
          direction,
        ];
      }

      return [field, direction];
    });

    const parsedFilterBy = canvassFilterSchema.parse(filterBy);
    const { canvassNumber } = this.utils.buildFilterWhereClause(parsedFilterBy);

    if (canvassNumber) {
      whereClause[this.Sequelize.Op.or] = [
        this.Sequelize.literal(`
          CASE 
            WHEN "canvass_requisitions"."cs_number" IS NULL 
            THEN CONCAT('CS-TMP-', "requisition"."company_code",cs_letter, draft_cs_number)
            ELSE CONCAT('CS-', "requisition"."company_code", cs_letter, cs_number)
          END ILIKE '%${canvassNumber}%'
        `),
      ];
    }

    const results = await this.findAll({
      limit,
      page,
      paginate,
      order: orderClauses || defaultOrder,
      where: whereClause,
      include: [
        {
          association: 'requisition',
          as: 'requisition',
          attributes: [
            'id',
            'assignedTo',
            'status',
            'type',
            'dateRequired',
            'companyCode',
          ],
        },
        {
          required: false,
          association: 'canvassApprovers',
          as: 'latestApprover',
          attributes: ['id', 'userId', 'level', 'status', 'isAdhoc'],
          where: {
            status: 'pending',
          },
          include: [
            {
              required: false,
              association: 'approver',
              as: 'approver',
              attributes: ['id', 'firstName', 'lastName'],
            },
          ],
        },
      ],
    });

    const resultWithDraftStatus = results.data.map((canvasses) => ({
      ...canvasses,
      isDraft: !canvasses.csNumber,
    }));

    const sortedNextApprovers = resultWithDraftStatus.map((canvasses) => {
      const isPending = canvasses?.canvassApprovers?.length > 0;
      if (!isPending) return canvasses;

      const lowestLevel = canvasses?.canvassApprovers?.reduce(
        (lowest, approver) => Math.min(lowest, approver?.level),
        Infinity,
      );
      const filteredApprovers = canvasses?.canvassApprovers?.filter(
        (approver) => approver?.level === lowestLevel,
      );

      if (filteredApprovers?.length > 1) {
        return {
          ...canvasses,
          canvassApprovers: filteredApprovers.filter(
            (approver) => !approver?.isAdhoc,
          ),
        };
      }

      return {
        ...canvasses,
        canvassApprovers: filteredApprovers,
      };
    });

    return {
      data: sortedNextApprovers,
      total: results.total,
    };
  }

  async getCanvass(canvassId, options = {}) {
    return await this.getById(canvassId, {
      ...options,
      include: [
        {
          association: 'requisition',
          as: 'requisition',
          attributes: ['id', 'assignedTo', 'createdBy', 'type', 'companyCode'],
        },
      ],
    });
  }

  async getDistinctRsIds(supplierId) {
    const rsIds = await this.tableName.findAll({
      attributes: ['requisitionId'],
      distinct: true,
      include: [
        {
          model: this.db.canvassItemModel,
          as: 'canvassItems',
          include: [
            {
              model: this.db.canvassItemSupplierModel,
              as: 'suppliers',
              where: {
                supplierId,
              },
            },
          ],
        },
      ],
    });

    return rsIds.map((item) => item.requisitionId);
  }
}

module.exports = CanvassRequisitionRepository;
