const BaseRepository = require('./baseRepository');

class SupplierRepository extends BaseRepository {
  constructor ({ db, clientErrors, attachmentRepository, commentRepository }) {
    super(db.supplierModel);
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.attachmentRepository = attachmentRepository;
    this.commentRepository = commentRepository;
  }

  async create(payload, options = {}) {
    const supplierCreated = await this.tableName.create(payload, {
      transaction: options.transaction,
    });

    return supplierCreated.toJSON();
  }

  async update(where, payload) {
    const updatedSupplier = await this.tableName.update(payload, {
      where,
    });

    return updatedSupplier;
  }

  async updateById(id, payload) {
    const updatedSupplier = await this.tableName.update(payload, {
      where: { id },
      returning: true,
    });

    if (!updatedSupplier[0]) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Supplier not found',
      });
    }

    return updatedSupplier[1][0].get({ plain: true });
  }

  async getSupplierByName(name) {
    const supplier = await this.tableName.findOne({
      where: { name },
    });

    return supplier?.get({ plain: true });
  }

  async getSupplierDetails(
    id,
    attachmentDateFrom,
    attachmentDateTo,
    commentDateFrom,
    commentDateTo,
  ) {
    // Build attachment where clause
    const attachmentWhere = { model: 'supplier' };
    if (attachmentDateFrom && attachmentDateTo) {
      attachmentWhere.createdAt = {
        [this.Sequelize.Op.between]: [attachmentDateFrom, attachmentDateTo],
      };
    }

    // Build comment where clause
    const commentWhere = { model: 'supplier' };
    if (commentDateFrom && commentDateTo) {
      commentWhere.createdAt = {
        [this.Sequelize.Op.between]: [commentDateFrom, commentDateTo],
      };
    }

    // select all suppliers with attachments and comments where id is equal to the id and model is supplier
    const supplier = await this.tableName.findByPk(id, {
      where: { id },
      include: [
        {
          model: this.attachmentRepository.model,
          as: 'attachments',
          where: attachmentWhere,
          required: false,
        },
        {
          model: this.commentRepository.model,
          as: 'comments',
          where: commentWhere,
          required: false,
        },
      ],
    });

    if (!supplier) {
      throw this.clientErrors.NOT_FOUND({
        message: `Supplier with id of ${id} not found`,
      });
    }

    return supplier?.get({ plain: true });
  }

  async getSupplierById(id) {
    const supplier = await this.tableName.findByPk(id);

    if (!supplier) {
      throw this.clientErrors.NOT_FOUND({
        message: `Supplier with id of ${id} not found`,
      });
    }

    return supplier?.get({ plain: true });
  }

  async destroy(id) {
    const deletedSupplier = await this.tableName.destroy({
      where: { id },
    });

    if (!deletedSupplier) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Supplier not found',
      });
    }

    return true;
  }

  async bulkCreate(payload) {
    // TODO Fix the updateOnDuplicate
    return await this.tableName.bulkCreate(payload, {
      where: { status: 'ACTIVE' },
      updateOnDuplicate: [
        'name',
        'contact',
        'tin',
        'address',
        'contactPerson',
        'contactNumber',
        'citizenshipCode',
        'natureOfIncome',
        'iccode',
      ],
    });
  }

  async getAllSuppliers(payload) {
    let whereClause = {};
    const {
      search,
      limit,
      page,
      order = [['name', 'ASC']],
      filterBy,
    } = payload;

    const {
      name,
      status,
    } = filterBy;

    if (name) {
      whereClause[this.Sequelize.Op.or] = [
        { name: { [this.Sequelize.Op.iLike]: `%${name}%` } },
        // { payCode: { [this.Sequelize.Op.iLike]: `%${name}%` } },
        // { initial: { [this.Sequelize.Op.iLike]: `%${name}%` } },
        // { iccode: { [this.Sequelize.Op.iLike]: `%${name}%` } },
        // { status: { [this.Sequelize.Op.iLike]: `%${name}%` } },
      ];
    }

    if (status) {
      whereClause[this.Sequelize.Op.or] = [{ status }];
    }

    if (search) {
      whereClause.name = { [this.Sequelize.Op.iLike]: `%${search}%` };
    }


    const orderClauses = order?.map(([field, direction]) => {
      const dir = direction.toUpperCase()

      if (field === 'name') {
        /// remove spaces at the start before sorting
        return [
          this.Sequelize.fn('LTRIM', this.Sequelize.col('name')),
          dir,
        ]
      }

      if (field === 'citizenshipCode') {
        /**
         * Note that prs-frontend transforms the returned citizenshipCode into this:
         * `return <span>{citizenshipCode === 'F' ? 'Filipino' : 'International'}</span>;` (see SupplierCitizenship.jsx line 5)
         * I just followed that logic and just noting it here if ever we decide to display citizenships other than Filipino and International.
        */
        const expr = this.Sequelize.literal(
          `CASE WHEN "citizenship_code" = 'F' THEN 'F' ELSE 'I' END`
        )
        return [expr, dir]
      }
      return [field, dir]
    })

    return await this.findAll({
      limit,
      page,
      order: orderClauses,
      where: whereClause,
    });
  }

  async getAllActiveSuppliers() {
    return await this.findAll({
      attributes: ['id', 'name'],
      where: { status: 'ACTIVE' },
      order: [['name', 'ASC']],
      limit: 100,
    });
  }

  async findSuppliersByIds(supplierIds, options = {}) {
    return this.findAll({
      where: {
        id: {
          [this.Sequelize.Op.in]: supplierIds,
        },
      },
      ...options,
    });
  }
}

module.exports = SupplierRepository;
