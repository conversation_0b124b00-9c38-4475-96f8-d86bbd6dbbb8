'use strict';

const {
  withTimescaleDBCompression,
} = require('../utils/timescale-db-migration-helper');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await withTimescaleDBCompression(
        queryInterface,
        transaction,
        'invoice_reports',
        async () => {
          await queryInterface.changeColumn(
            'invoice_reports',
            'invoice_amount',
            {
              type: Sequelize.DECIMAL(17, 2),
            },
            { transaction },
          );
        },
      );

      await withTimescaleDBCompression(
        queryInterface,
        transaction,
        'invoice_report_histories',
        async () => {
          await queryInterface.changeColumn(
            'invoice_report_histories',
            'invoice_amount',
            {
              type: Sequelize.DECIMAL(17, 2),
            },
            { transaction },
          );
        },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    // No down migration needed
  },
};
