'use strict';
const { withTimescaleDBCompression } = require('../utils/timescale-db-migration-helper');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await withTimescaleDBCompression(queryInterface, transaction, 'leaves', async () => {
        await queryInterface.addColumn('leaves', 'rs__user_id', {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
        });

        await queryInterface.addColumn('leaves', 'canvass_user_id', {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
        });

        await queryInterface.addColumn('leaves', 'po_user_id', {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
        });

        await queryInterface.addColumn('leaves', 'pr_user_id', {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
        });
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await withTimescaleDBCompression(queryInterface, transaction, 'leaves', async () => {
        await queryInterface.removeColumn(
          'leaves',
          'rs__user_id',
        );

        await queryInterface.removeColumn(
          'leaves',
          'canvass_user_id',
        );

        await queryInterface.removeColumn(
          'leaves',
          'po_user_id',
        );

        await queryInterface.removeColumn(
          'leaves',
          'pr_user_id',
        );
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
