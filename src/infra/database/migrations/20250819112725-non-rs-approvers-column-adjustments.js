'use strict';

/** @type {import('sequelize-cli').Migration} */
('use strict');
const {
  withTimescaleDBCompression,
} = require('../utils/timescale-db-migration-helper');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await withTimescaleDBCompression(
        queryInterface,
        transaction,
        'non_requisition_approvers',
        async () => {
          await queryInterface.changeColumn(
            'non_requisition_approvers',
            'non_requisition_id',
            {
              type: Sequelize.INTEGER,
              allowNull: true,
            },
          );

          await queryInterface.changeColumn(
            'non_requisition_approvers',
            'role_id',
            {
              type: Sequelize.INTEGER,
              allowNull: true,
            },
          );
        },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await withTimescaleDBCompression(
        queryInterface,
        transaction,
        'non_requisition_approvers',
        async () => {
          await queryInterface.changeColumn(
            'non_requisition_approvers',
            'non_requisition_id',
            {
              type: Sequelize.INTEGER,
              allowNull: false,
            },
          );

          await queryInterface.changeColumn(
            'non_requisition_approvers',
            'role_id',
            {
              type: Sequelize.INTEGER,
              allowNull: false,
            },
          );
        },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
