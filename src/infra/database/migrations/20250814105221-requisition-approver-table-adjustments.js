'use strict';

/** @type {import('sequelize-cli').Migration} */
('use strict');
const {
  withTimescaleDBCompression,
} = require('../utils/timescale-db-migration-helper');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await withTimescaleDBCompression(
        queryInterface,
        transaction,
        'requisition_approvers',
        async () => {
          await queryInterface.changeColumn(
            'requisition_approvers',
            'model_id',
            {
              type: Sequelize.INTEGER,
              allowNull: true,
            },
          );
        },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await withTimescaleDBCompression(
        queryInterface,
        transaction,
        'requisition_approvers',
        async () => {
          await queryInterface.changeColumn(
            'requisition_approvers',
            'model_id',
            {
              type: Sequelize.INTEGER,
              allowNull: false,
            },
          );
        },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
