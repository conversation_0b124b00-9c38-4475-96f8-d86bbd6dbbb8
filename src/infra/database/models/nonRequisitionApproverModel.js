const {
  NON_RS_APPROVER_STATUS,
} = require('../../../domain/constants/nonRSConstants');

module.exports = (sequelize, Sequelize) => {
  const NonRequisitionApproverModel = sequelize.define(
    'non_requisition_approvers',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      nonRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'non_requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'non_requisition_id',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'user_id',
      },
      altApproverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'alt_approver_id',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
        defaultValue: NON_RS_APPROVER_STATUS.PENDING,
      },
      roleId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'roles',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'role_id',
      },
      isAdhoc: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_adhoc',
      },
      overrideBy: {
        type: Sequelize.JSONB,
        field: 'override_by',
        defaultValue: null,
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  NonRequisitionApproverModel.associate = (models) => {
    NonRequisitionApproverModel.belongsTo(models.userModel, {
      foreignKey: 'userId',
      as: 'approver',
    });

    NonRequisitionApproverModel.belongsTo(models.roleModel, {
      foreignKey: 'roleId',
      as: 'role',
    });

    NonRequisitionApproverModel.belongsTo(models.userModel, {
      foreignKey: 'altApproverId',
      as: 'altApprover',
    });
  };

  return NonRequisitionApproverModel;
};
