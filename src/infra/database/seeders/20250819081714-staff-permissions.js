'use strict';
const { USER_TYPES } = require('../../../domain/constants/userConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const dateNow = new Date();

    await queryInterface.sequelize.transaction(async (transaction) => {
      const roles = await queryInterface.sequelize.query(
        `SELECT id FROM roles WHERE name = :name`,
        {
          replacements: { name: USER_TYPES.AREA_STAFF },
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      if (!roles.length) {
        console.log('Role not found');
        return;
      }

      const areaStaffRoleId = roles[0].id;

      const paymentPermissions = await queryInterface.sequelize.query(
        `SELECT id FROM permissions WHERE module = 'payments'`,
        {
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      if (!paymentPermissions.length) {
        console.log('No payment permissions defined; nothing to assign');
        return;
      }

      const permissionIds = paymentPermissions.map((p) => p.id);
      const existingRolePermissions = await queryInterface.sequelize.query(
        `SELECT permission_id FROM role_permissions 
         WHERE role_id = :roleId 
           AND permission_id IN (:permissionIds)`,
        {
          replacements: { roleId: areaStaffRoleId, permissionIds },
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      const existingPermissionIdSet = new Set(
        existingRolePermissions.map((rp) => rp.permission_id),
      );

      const newRolePermissions = permissionIds
        .filter((id) => !existingPermissionIdSet.has(id))
        .map((permissionId) => ({
          role_id: areaStaffRoleId,
          permission_id: permissionId,
          created_at: dateNow,
          updated_at: dateNow,
        }));

      if (newRolePermissions.length > 0) {
        await queryInterface.bulkInsert(
          'role_permissions',
          newRolePermissions,
          { transaction },
        );
      }
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      const roles = await queryInterface.sequelize.query(
        `SELECT id FROM roles WHERE name = :name`,
        {
          replacements: { name: USER_TYPES.AREA_STAFF },
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      if (!roles.length) return;

      const areaStaffRoleId = roles[0].id;

      const paymentPermissions = await queryInterface.sequelize.query(
        `SELECT id FROM permissions WHERE module = 'payments'`,
        {
          type: queryInterface.sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      if (!paymentPermissions.length) return;

      const permissionIds = paymentPermissions.map((p) => p.id);

      await queryInterface.bulkDelete(
        'role_permissions',
        {
          role_id: areaStaffRoleId,
          permission_id: { [Sequelize.Op.in]: permissionIds },
        },
        { transaction },
      );
    });
  },
};
