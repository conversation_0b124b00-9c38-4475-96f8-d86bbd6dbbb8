<html lang='en'>

<head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>Purchase Request System</title>
    <link rel='preconnect' href='https://rsms.me/' />
    <link rel='stylesheet' href='https://rsms.me/inter/inter.css' />
    <style>
        @page {
            size: legal;
            margin: .3cm;
        }

        body {
            font-family: 'Inter', sans-serif;
            font-size: 12px;
            line-height: 1.2;
            color: #000;
            margin: 0;
            padding: 0;
            width: 8.5in;
            height: 11in;
            box-sizing: border-box;
        }

        /* Support for Inter variable font */
        @supports (font-variation-settings: normal) {
            body {
                font-family: 'Inter var', sans-serif;
            }
        }

        .wrapper {
            padding-top: 30px !important;
            max-width: 7.5in;
            margin: 0 auto;
            position: relative;
            padding: 0;
        }

        .header {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: flex-start;
        }


        .header>div {
            flex: 50%;
            /* or - flex: 0 50% - or - flex-basis: 50% - */
            /*demo*/
        }

        .logo-title {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .po-title {
            margin-top: 10px;
            font-size: 16px;
            color: #754445;
        }

        .logo {
            font-size: 20px;
            line-height: 1;
        }

        .title {
            font-weight: bold;
            font-size: 12px;
            color: #800000;
        }

        .page-info {
            text-align: right;
            font-size: 12px;
            height: 14px;
        }

        .po-section {
            flex: none;
            font-size: 17px;
        }

        .date-extracted-section {
            text-align: right;
            font-size: 10px;
            line-height: 15px;
            vertical-align: middle;
            height: 15px;
        }

        .project-company-header {
            font-weight: bold;
            display: flex;
            margin-top: 10px;
            justify-content: center;
            align-items: center;
            text-align: center;
            width: 100%;
            border-bottom: 1px solid #000;
        }

        .pr-number {
            margin-top: 10px;
            display: flex;
            align-items: center;
            justify-content: left;
            /*gap: 10px;*/
        }

        .pr-label {
            font-size: 14px;
            font-weight: bold;
        }

        .pr-value {
            text-decoration: underline;
            font-weight: bold;
        }

        .date-section {
            display: flex;
            align-items: center;
        }

        .date-label {
            color: #323232;
            font-size: 7px;
            margin-left: 30px;
        }

        .date-value {
            font-size: 8px;
            font-weight: bold;
        }

        .note-section {
            font-size: 10px;
            height: 14px;
        }

        .bottom-note-section {
            font-size: 10px;
            text-align: right;
            font-style: italic;
            width: 100%;
        }

        .amount-words {
            font-style: italic;
            font-weight: bold;
        }

        .bottom-note-section input {
            border: none;
            width: 30%;
            font-size: 8px;
            margin-left: 5px;
            font-style: italic;
        }

        .section {
            margin: 10px 0;
            gap: 10px;


            .details-grid-outer2 {
                font-size: 10px;
                border: 1px solid #ccc;
                border-radius: 10px;
                padding: 10px;
                margin-bottom: 10px;
            }
        }

        .details-grid-outer2 {
            font-size: 10px;
            border: 1px solid #ccc;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 10px;
        }

        .section-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 13px;
        }

        .section-description {
            margin-bottom: 5px;
            font-size: 9px;
        }

        .details-grid-outer {
            border: 1px solid #ccc;
            border-radius: 10px;
            padding: 10px;
            padding-bottom: 0px;

            .details-grid {
                display: flex;
                gap: 100px
            }

        }

        .signing-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 10px;
            padding: 10px;
            font-size: 10px;
        }

        .details-row {
            font-size: 10px !important;
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
        }

        .details-label {
            color: #323232;
            font-weight: normal;
            font-size: 10px;
            margin-bottom: 4px;
        }

        .signing-details-label {
            color: #323232;
            font-weight: normal;
            font-size: 8px;
        }

        .signing-details-label input {
            margin-top: 20px;
            border-bottom: solid 1px;
            width: 150px;
        }

        .checks-container {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 10px;
            width: 100%;
            padding: 0px 0 10px;
        }

        .check-item {
            display: flex;
            align-items: center;
            white-space: nowrap;
        }

        .check-item label {
            font-size: 8px;
            margin-right: 3px;
        }

        /* Style for the checkboxes */
        .check-item input[type='checkbox'] {
            width: 10px;
            height: 10px;
            margin-right: 3px;
        }

        .check-textfield {
            border: none;
            border-bottom: 1px solid #000;
            font-size: 7px;
            padding: 0;
            margin: 0;
        }

        input,
        select {
            border: none;
            background: transparent;
            width: 100%;
            font-family: inherit;
            font-size: inherit;
            outline: none;
        }

        .items-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }

        .items-title {
            font-weight: bold;
        }

        .table-container {
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #ccc;
            margin-top: 10px;
            margin-bottom: 10px;
            padding: 1px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            border: none;
        }

        th,
        td {
            border: 1px solid #ccc;
            padding: 4px;
            text-align: left;
            font-size: 10px;
        }

        /* Remove border from outer edges of table cells */
        tr:first-child th {
            border-top: none;
        }

        tr:last-child td {
            border-bottom: none;
        }

        th:first-child,
        td:first-child {
            border-left: none;
        }

        th:last-child,
        td:last-child {
            border-right: none;
        }

        th,
        td {
            padding: 4px;
            text-align: left;
            font-size: 11px;
            color: #4F575E;
        }

        th {
            color: #4f575e;
            vertical-align: top;
            text-align: left;
            font-weight: 500;
            font-size: 9px;
        }

        td {
            height: 30px;
            font-size: 9px;
        }

        .base-col {
            width: 80px;
        }

        .num-col {
            width: 20px;
        }

        .unit-col {
            max-width: 50px;
            vertical-align: center;
            word-break: break-all;
            word-wrap: break-word;
            white-space: normal;
        }

        .discount-col {
            width: 40px;
        }

        .pricing-col {
            width: 60px;
        }

        .pricing-col input {
            width: 90%;
            margin-right: 5px;
        }

        .item-name-cell {
            vertical-align: center;
            word-break: break-all;
            word-wrap: break-word;
            white-space: normal;
            padding: 4px;
            max-width: 150px;
        }

        .item-name-input {
            color: #4f575e;
            width: 100%;
            outline: none;
            border: none;
            background: transparent;
            font-family: inherit;
            font-size: inherit;
            text-decoration: underline;
            word-break: break-all;
            word-wrap: break-word;
            white-space: normal;
            text-align: left;
            resize: none;
            overflow: hidden;
        }

        .text-left {
            text-align: left;
        }

        .text-right {
            text-align: right;
        }

        .footer {
            position: absolute;
            bottom: 5px;
            text-align: left;
            font-size: 10px;
        }

        .main-title {
            text-align: center;
            font-size: 17px;

            span {
                font-weight: bold;
                display: inline-block;
            }

            div {
                font-size: 10px;

                .input {
                    display: inline-block;
                    width: 80px;
                }
            }
        }

        .project-name-2 {
            font-weight: 700;
            font-size: 17px;
            vertical-align: middle;

        }

        .po-section-2 {
            font-weight: 400;
            font-size: 11px;
            vertical-align: middle;

        }

        .details-row-2 {
            font-weight: 400;
            font-size: 10px;
            vertical-align: middle;
            text-transform: capitalize;

        }

        .left-align {
            text-align: right;
        }

        .flex-col {
            flex-direction: column;

        }

        .footer-wrapper {
            display: flex;
            flex-direction: column;
        }

        .sub-header {
            margin-top: 10px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: flex-start;
        }

        .extraction-container {
            margin-right: 10px;
        }

        .note-header {
            margin-top: 15px;
            font-size: 10px;
        }

        .flex {
            display: flex;
            gap: 10px;
        }

        .checkbox {
            width: 10px;
            margin: 0px;
            margin-top: 5px;
            position: relative;
            top: 3px;
        }

        .flex-gap {
            gap: 40px;
        }

        .bottom-gap {
            margin-bottom: 8px;
        }

        .gap-right {
            margin-left: 25px;
            width: 100px;
        }

        .justify-content {
            justify-content: space-between;
        }

        .signature {
            margin-top: 25px
        }

        .f-10 {
            font-size: 10px;
        }

        .grow {
            flex-grow: 0.5;
            align-items: stretch;
        }

        .h-full {
            height: 50%;
        }

        .h-fuller {
            height: 100%;
        }

        .flex-column {
            display: flex;
            flex-direction: column;
            height: 95vh;
        }
    </style>
</head>

<body>
    <div class='body'>

        {{#each pagesData as |pageData pageIndex|}}
        <div class='wrapper' {{#unless @first}}style="page-break-before: always;" {{/unless}}>
            <div class="flex-column">
                <div>
                    {{#if @first}}
                    <div>
                        <div class='header'>
                            <div class='po-title'>
                                <b>PAYMENT REQUEST</b>
                            </div>

                            <div class='page-info'>
                                Page
                                <b>{{@pageData.page}}</b>
                                of
                                {{@root.totalPages}}
                            </div>

                        </div>

                        <div class='main-title'>
                            <span>{{@root.projectName}}</span>
                        </div>

                        <div>
                            <div class='project-company-header'></div>
                        </div>

                        <div class='sub-header'>
                            <div class='po-section'>
                                <span>
                                    <b>V.R.# {{@root.prNumber}}</b>
                                </span>
                            </div>

                            <div class='date-extracted-section'>
                                <span>
                                    <span class="extraction-container">
                                        Date Prepared: <b>{{@root.datePrepared}}</b>
                                    </span>
                                    <span class="extraction-container">
                                        Date Needed: <b>{{@root.dateNeeded}}</b>
                                    </span>
                                    <span class="extraction-container">
                                        Time Needed: <b>{{@root.timeNeeded}}</b>
                                    </span>

                                </span>
                            </div>
                        </div>

                        <div class='note-header'> <b>NOTE:</b> Processing of voucher request upon receipt of Accounting
                            takes three (3) days.</div>

                        <div class='section'>
                            <div class='details-grid-outer'>
                                <div class='section-title'>Request Details</div>
                                <div class='details-grid'>
                                    <div class='details-row'>
                                        <div class='details-label'>Payable To:</div>
                                        <div><b>{{@root.payableTo}}</b></div>
                                    </div>
                                    <div class='details-row'>
                                        <div class='details-label'>TIN:</div>
                                        <div><b>{{@root.vendorTin}}</b></div>
                                    </div>
                                    <div class='details-row'>
                                        <div class='details-label'>Total Amount in Figures:</div>
                                        <div><b>{{@root.totalAmount}}</b></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {{else}}
                    <div>
                        <div class='header'>
                            <div class='po-title'>
                                <b>PAYMENT REQUEST</b>
                            </div>

                            <div class='page-info'>
                                Page
                                <b>{{@pageData.page}}</b>
                                of
                                {{@root.totalPages}}
                            </div>

                        </div>

                        <div class='main-title'>
                            <span>{{@root.projectName}}</span>
                        </div>

                        <div>
                            <div class='project-company-header'></div>
                        </div>

                        <div class='sub-header'>
                            <div class='po-section'>
                                <span>
                                    <b>V.R.# {{@root.prNumber}}</b>
                                </span>
                            </div>

                            <div class='date-extracted-section'>
                                <span>
                                    <span class="extraction-container">
                                        Date Prepared: <b>{{@root.datePrepared}}</b>
                                    </span>
                                    <span class="extraction-container">
                                        Date Needed: <b>{{@root.dateNeeded}}</b>
                                    </span>
                                    <span class="extraction-container">
                                        Time Needed: <b>{{@root.timeNeeded}}</b>
                                    </span>

                                </span>
                            </div>
                        </div>

                    </div>
                    {{/if}}

                    <div class='items-header'>
                        <div class='items-title'>Items</div>
                        <div>
                            <b>
                                {{@pageData.from}}
                                -
                                {{@pageData.to}}
                            </b>

                            of
                            {{@root.totalItems}}
                        </div>
                    </div>

                    <div class='table-container'>
                        <table>
                            <thead>
                                <tr>
                                    <th>
                                        <center>#</center>
                                    </th>
                                    <th>Item Name</th>
                                    <th>Qty</th>
                                    <th>Unit</th>
                                    <th>Unit Price</th>
                                    <th>Discount</th>
                                    <th>Total Price</th>
                                    <th>R.S. #</th>
                                    <th>DEL No</th>
                                    <th>SI</th>
                                    <th>ACCT. Code</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each @pageData.data}}
                                <tr>
                                    <td class='num-col'>
                                        <center>{{id}}</center>
                                    </td>
                                    <td class='item-name-cell'>
                                        <u>{{itemName}}</u>
                                    </td>
                                    <td class='unit-col'>
                                        {{qty}}
                                    </td>
                                    <td class='unit-col'>
                                        {{unit}}
                                    </td>
                                    <td class='pricing-col text-right'>
                                        {{unitPrice}}
                                    </td>
                                    <td class='discount-col text-left'>
                                        {{discount}}
                                    </td>
                                    <td class='pricing-col text-right'>
                                        {{totalPrice}}
                                    </td>
                                    <td class='pricing-col'>
                                        {{rsNumber}}
                                    </td>
                                    <td class='unit-col'>
                                        {{supplierDeliveryNumber}}
                                    </td>
                                    <td class='unit-col'>
                                        {{supplierInvoiceNumber}}
                                    </td>
                                    <td>
                                        {{accountCode}}
                                    </td>
                                </tr>
                                {{/each}}
                                {{#if @last}}
                                {{#each @root.additionalFees}}
                                        <tr>
                                        <td class='num-col'>
                                        </td>
                                        <td class='item-name-cell'>
                                        </td>
                                        <td class='unit-col'>
                                        </td>
                                        <td class='unit-col'>
                                        </td>
                                        <td class='pricing-col text-left'>
                                        </td>
                                        <td class='discount-col text-left'>
                                        </td>
                                        <td class='pricing-col text-right'>
                                            <b>{{fee}}</b>
                                        </td>
                                        <td class='pricing-col text-left'>
                                        </td>
                                        <td class='unit-col'>
                                        </td>
                                        <td class='unit-col'>
                                        </td>
                                        <td>
                                        </td>
                                    </tr>
                                {{/each}}
                                
                                <tr>
                                    <td class='num-col'>
                                    </td>
                                    <td class='item-name-cell'>
                                    </td>
                                    <td class='unit-col'>
                                    </td>
                                    <td class='unit-col'>
                                    </td>
                                    <td class='pricing-col text-left'>
                                    </td>
                                    <td class='discount-col text-left'>
                                    </td>
                                    <td class='pricing-col text-right'>
                                        <b>{{@root.grandTotal}}</b>
                                    </td>
                                    <td class='pricing-col text-left'>
                                    </td>
                                    <td class='unit-col'>
                                    </td>
                                    <td class='unit-col'>
                                    </td>
                                    <td>
                                    </td>
                                </tr>
                                {{/if}}
                            </tbody>
                        </table>
                    </div>


                </div>

                {{#if @last}}
                <div class="footer-wrapper">
                    <div class="bottom-note-section">
                        Total Amount in words: <b>{{@root.amountInWords}}</b>
                    </div>

                    </br>

                    <div class="">
                        <div class="section-terms">
                            <div class="details-grid-outer2">
                                <div>
                                    Checks pay to “Cash” uncrossed must be approved by Pres. / EVP SVP. Please issue
                                    checks.
                                </div>
                                <div class='flex'>
                                    <div>
                                        <input type="checkbox" class='checkbox' />
                                        <span class="label">Pay To Cash ________________</span>
                                    </div>
                                    <div>
                                        <input type="checkbox" class='checkbox' />
                                        <span class="label">For Encashment ________________</span>
                                    </div>
                                    <div>
                                        <input type="checkbox" class='checkbox' />
                                        <span class="label">Uncrossed Check ________________</span>
                                    </div>
                                    <div>
                                        <input type="checkbox" class='checkbox' />
                                        <span class="label">Manager’s Check ________________</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        </br>


                        <div class="section-terms">
                            <div class="details-grid-outer2">

                                <div class='flex'>
                                    <div>
                                        <div>
                                            Supporting Documents:
                                        </div>
                                        <div class='flex flex-gap'>

                                            <div>
                                                <input type="checkbox" class='checkbox' />
                                                <span class="label">Attached</span>
                                            </div>
                                            <div>
                                                <input type="checkbox" class='checkbox' />
                                                <span class="label">To Follow</span>
                                            </div>
                                            <div>
                                                <input type="checkbox" class='checkbox' />
                                                <span class="label">None Available</span>
                                            </div>
                                            <div>
                                                <input type="checkbox" class='checkbox' />
                                                <span class="label">Orig. RS/OS/CS</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class='gap-right'>
                                        <div class='bottom-gap'>
                                            Charge to Company:
                                        </div>
                                        <div>
                                            <b>{{@root.chargeTo}}</b>
                                        </div>
                                    </div>

                                    <div class='gap-right'>
                                        <div class='bottom-gap'>
                                            Project:
                                        </div>
                                        <div>
                                            <b>{{@root.projectInitial}}</b>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        </br>

                        <div class="section-terms">
                            <div class="details-grid-outer2">

                                <div class='flex justify-content'>
                                    <div>
                                        <div class='bottom-gap'>
                                            Requested by:
                                        </div>
                                        <div class='signature'>
                                            ___________________________________
                                        </div>
                                    </div>

                                    <div>
                                        <div class='bottom-gap'>
                                            Endorsed by:
                                        </div>
                                        <div class='signature'>
                                            ___________________________________
                                        </div>
                                    </div>

                                    <div>
                                        <div class='bottom-gap'>
                                            Approved by:
                                        </div>
                                        <div class='signature'>
                                            ___________________________________
                                        </div>
                                    </div>

                                    <div>
                                        <div class='bottom-gap'>
                                            Countersigned by:
                                        </div>
                                        <div class='signature'>
                                            ___________________________________
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        </br>

                        <div class='f-10'>
                            Notes:
                        </div>


                    </div>





                </div>

                <div class="section grow">
                    <div class="section-terms">
                        <div class="details-grid-outer2 h-fuller">

                        </div>
                    </div>
                </div>

                <div class="footer">
                    <div>
                        No. of printing made: <b>
                            {{@root.totalPages}}</b>
                    </div>
                </div>


                {{/if}}



            </div>



        </div>
        {{/each}}

    </div>
</body>

</html>
