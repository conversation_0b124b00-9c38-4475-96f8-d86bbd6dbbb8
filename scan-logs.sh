#!/bin/sh

# Set the log directory
LOG_DIR="/usr/app/logs"

# Get parameters
SEARCH_PATTERN="$1"
DATE_PREFIX="$2"

# Validate search pattern
if [ -z "$SEARCH_PATTERN" ]; then
  echo "Usage: $0 <grep-pattern> [YYYYMMDD]"
  exit 1
fi

# Determine log files to search
if [ -n "$DATE_PREFIX" ]; then
  LOG_FILES=$(ls "$LOG_DIR"/${DATE_PREFIX}-*-app.log 2>/dev/null)
else
  LOG_FILES="$LOG_DIR/app.log"
fi

# Check if any files match
if [ -z "$LOG_FILES" ]; then
  echo "No log files found in $LOG_DIR for date prefix: $DATE_PREFIX"
  exit 1
fi
  
# Process logs (no jq)
grep "$SEARCH_PATTERN" $LOG_FILES
